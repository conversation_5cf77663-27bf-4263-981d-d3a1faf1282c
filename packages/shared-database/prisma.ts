import { PrismaClient } from "@prisma/client";
import {
  extendActivityLogMiddleware,
  extendTenantCreationMiddleware,
  extendWebhookTriggerMiddleware,
  extendContactInventorySyncMiddleware,
  extendQuoteCartSyncMiddleware,
} from "./extend-middleware";
import { createSoftDeleteExtension } from "./extensions/soft-delete";
import { createPaginationExtension } from "./extensions/pagination";

let prisma: any;

if (process.env.NODE_ENV === "production") {
  prisma = new PrismaClient();
} else {
  if (!(global as any).prisma) {
    (global as any).prisma = new PrismaClient({
      log:
        process.env.NODE_ENV === "development" ? ["error", "warn"] : ["error"],
    });
  }
  prisma = (global as any).prisma;
}

prisma = prisma.$extends(createSoftDeleteExtension());
prisma = prisma.$extends(createPaginationExtension());
prisma = prisma.$extends(extendActivityLogMiddleware());
prisma = prisma.$extends(extendWebhookTriggerMiddleware());
prisma = prisma.$extends(extendTenantCreationMiddleware());
prisma = prisma.$extends(extendContactInventorySyncMiddleware());
prisma = prisma.$extends(extendQuoteCartSyncMiddleware());

export default prisma;
export { prisma };
