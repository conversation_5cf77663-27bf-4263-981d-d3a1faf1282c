"use client";

import type { Contact } from "@/types/contact";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDelete } from "@flinkk/hooks";
import { getContactsTableColumns } from "./columns";
import { useRouter } from "next/navigation";

import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog,
    ),
  {
    ssr: false,
  },
);

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface ContactsTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

export function ContactsTable({
  data,
  pageCount,
  permissions,
}: ContactsTableProps) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Contact> | null>(null);

  const { deleteRecord } = useDelete({
    model: "contact",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setRowAction(null);
      router.refresh();
    },
  });

  const handleDeleteContact = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      await deleteRecord(rowAction.row.id);
    }
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    }
  }, [rowAction]);

  const columns = React.useMemo(
    () =>
      getContactsTableColumns({
        setRowAction,
        permissions,
      }),
    [setRowAction, permissions],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Construct full name from firstName and lastName
          const firstName = row.firstName || "";
          const lastName = row.lastName || "";
          const fullName = `${firstName}${lastName}`.trim();

          // Create URL with title parameter
          const url = `/contacts/${row.id}/view?title=${encodeURIComponent(fullName)}`;
          router.push(url);
        }}
        doctype="contact"
      >
        <DataTableToolbar
          table={table}
          buttonText={permissions.canCreate ? "New Contact" : undefined}
          href={permissions.canCreate ? "/contacts/new" : undefined}
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && permissions.canDelete && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeleteContact}
        />
      )}
    </>
  );
}
