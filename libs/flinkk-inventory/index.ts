/**
 * FlinkkInventoryAPI - Centralized inventory API client for Flinkk applications
 *
 * This library provides a clean, type-safe interface for inventory service operations
 * including connection verification and future inventory management features.
 *
 * Key Features:
 * - Explicit runtime configuration (no environment variables)
 * - Type-safe API interactions
 * - Robust error handling
 * - Extensible for future inventory operations
 */

// ============================================================================
// Type Definitions
// ============================================================================

export interface FlinkkInventoryAPIConfig {
  apiUrl: string;
  token: string;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface VerifyConnectionResponse {
  status: "connected" | "disconnected";
  message: string;
  timestamp: string;
  version?: string;
}

export interface ProductService {
  id: string;
  name: string;
  description?: string;
  category_id?: string;
  price?: number;
  currency?: string;
  [key: string]: any;
}

export interface Hotel {
  id: string;
  name: string;
  description?: string;
  location?: string;
  rating?: number;
  amenities?: string[];
  [key: string]: any;
}

export interface SupplierOffering {
  id: string;
  product_service_id: string;
  supplier_id?: string;
  price?: number;
  currency?: string;
  availability?: any;
  [key: string]: any;
}

export interface AddonCategory {
  id: string;
  name: string;
  description?: string;
  is_active?: boolean;
  [key: string]: any;
}

export interface GetProductsServicesParams {
  category_id?: string;
  search?: string;
  limit?: number;
  page?: number;
}

export interface GetHotelsParams {
  search?: string;
  destination_id?: string;
  limit?: number;
  page?: number;
}

export interface GetDestinationsParams {
  search?: string;
  limit?: number;
  page?: number;
}

export interface Destination {
  id: string;
  name: string;
  description?: string;
  country?: string;
  region?: string;
  [key: string]: any;
}

export interface GetSupplierOfferingsParams {
  product_service_id: string;
  start_date?: string;
  end_date?: string;
  limit?: number;
  page?: number;
}

export interface GetAddonCategoriesParams {
  is_active?: boolean;
  limit?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
}

export interface RoomConfiguration {
  id: string;
  title: string;
  description?: string;
  capacity?: number;
  basePrice?: number;
  [key: string]: any;
}

export interface Room {
  room_id: string;
  product_id: string;
  room_name: string;
  room_number?: string;
  [key: string]: any;
}

export interface AvailabilityRecord {
  room_id: string;
  room_number: string;
  room_name: string;
  config_name: string;
  from_date: string;
  to_date: string;
  status: string;
  quantity: number;
  dynamic_price?: number | null;
  notes?: string;
  order_id?: string;
}

export interface HotelAvailabilityResponse {
  room_configs: RoomConfiguration[];
  rooms: Room[];
  availability: AvailabilityRecord[];
}

export interface BasePriceRule {
  id: string;
  amount: string;
  currency_code: string;
  description?: string;
  hotel_id: string;
  room_config_id: string;
  occupancy_type_id: string;
  meal_plan_id: string;
  min_occupancy: number;
  max_occupancy?: number;
  default_gross_cost: string;
  default_fixed_margin?: string;
  default_margin_percentage: string;
  default_total: string;
  monday_price: string;
  tuesday_price: string;
  wednesday_price: string;
  thursday_price: string;
  friday_price: string;
  saturday_price: string;
  sunday_price: string;
  monday_gross_cost: string;
  monday_fixed_margin: string;
  monday_margin_percentage: string;
  tuesday_gross_cost: string;
  tuesday_fixed_margin: string;
  tuesday_margin_percentage: string;
  wednesday_gross_cost: string;
  wednesday_fixed_margin: string;
  wednesday_margin_percentage: string;
  thursday_gross_cost: string;
  thursday_fixed_margin: string;
  thursday_margin_percentage: string;
  friday_gross_cost: string;
  friday_fixed_margin: string;
  friday_margin_percentage: string;
  saturday_gross_cost: string;
  saturday_fixed_margin: string;
  saturday_margin_percentage: string;
  sunday_gross_cost: string;
  sunday_fixed_margin: string;
  sunday_margin_percentage: string;
}

export interface SeasonalPriceRule {
  id: string;
  start_date: string;
  end_date: string;
  amount: string;
  currency_code: string;
  name?: string;
  description?: string;
  priority: number;
  base_price_rule_id: string;
  min_nights?: number;
  max_nights?: number;
  day_of_week_constraints?: string;
}

export interface MealPlan {
  id: string;
  name: string;
  title?: string;
  description?: string;
  is_active?: boolean;
}

export interface OccupancyConfig {
  id: string;
  name: string;
  type: string;
  hotel_id: string;
  min_age?: number;
  max_age?: number;
  min_occupancy: number;
  max_occupancy: number;
  is_default: boolean;
  metadata?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
  title?: string;
  description?: string;
  is_active?: boolean;
}

export interface HotelPricingResponse {
  success: boolean;
  hotel: {
    id: string;
    name: string;
    handle: string;
  };
  pricing: {
    base_price_rules: BasePriceRule[];
    seasonal_price_rules: SeasonalPriceRule[];
    meal_plans: MealPlan[];
    occupancy_configs: OccupancyConfig[];
    currency_code: string;
    store_context: boolean;
  };
}

export interface Customer {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  [key: string]: any;
}

export interface CustomerCheckResponse {
  exists: boolean;
  customer?: Customer;
}

export interface CustomerRegisterRequest {
  email: string;
  password: string;
}

export interface CustomerRegisterResponse {
  token: string;
  customer?: Customer;
  [key: string]: any;
}

export interface CustomerCreateRequest {
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
}

export interface CustomerCreateResponse {
  id: string;
  email: string;
  [key: string]: any;
}

export interface CartCreateRequest {
  region_id: string;
  customer_id?: string; // Optional - can create cart with just email
  email: string;
  currency_code: string;
}

export interface CartCreateResponse {
  cart: {
    id: string;
    region_id: string;
    customer_id?: string; // Optional - may not be present if cart created with just email
    email: string;
    currency_code: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface CartItem {
  variant_id: string;
  quantity: number;
  title?: string;
  unit_price: number;
  // Product-related fields at top level (optional for flexibility)
  product_collection?: string;
  product_description?: string;
  product_handle?: string;
  product_id?: string;
  product_subtitle?: string;
  product_title?: string;
  product_type?: string;
  product_type_id?: string;
  raw_compare_at_unit_price?: number;
  metadata?: {
    room_id?: string;
    hotel_id?: string;
    hotel_name?: string;
    start_date?: string;
    end_date?: string;
    room_config_id?: string;
    room_config_name?: string;
    number_of_rooms?: number;
    // Product-related fields also in metadata for consistency
    product_collection?: string;
    product_description?: string;
    product_handle?: string;
    product_id?: string;
    product_subtitle?: string;
    product_title?: string;
    product_type?: string;
    product_type_id?: string;
    raw_compare_at_unit_price?: number;
    // Occupancy and meal plan fields
    occupancy_type_id?: string;
    occupancy_type_name?: string;
    meal_plan_id?: string;
    meal_plan_name?: string;
    [key: string]: any;
  };
}

export interface AddItemsRequest {
  items: CartItem[];
}

export interface AddItemsResponse {
  cart: {
    id: string;
    items: Array<{
      id: string;
      variant_id: string;
      quantity: number;
      unit_price: number;
      metadata?: any;
      [key: string]: any;
    }>;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface CartConvertToOrderRequest {
  payment_type: "deposit" | "full" | "bank_transfer";
  partial_amount?: number;
  notes?: string;
  collected_by?: string;
}

export interface CartConvertToOrderResponse {
  order: {
    id: string;
    status: string;
    total: number;
    currency_code: string;
    payment_status: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface CartResponse {
  cart: {
    id: string;
    region_id: string;
    customer_id?: string;
    email: string;
    currency_code: string;
    total: number;
    subtotal: number;
    tax_total: number;
    items: {
      id: string;
      variant_id: string;
      title: string;
      quantity: number;
      unit_price: number;
      product_id?: string;
      product_title?: string;
      product_description?: string;
      product_subtitle?: string;
      product_type?: string;
      product_type_id?: string;
      product_collection?: string;
      product_handle?: string;
      raw_compare_at_unit_price?: number;
      metadata?: {
        room_id?: string;
        hotel_id?: string;
        hotel_name?: string;
        start_date?: string;
        end_date?: string;
        room_config_id?: string;
        room_config_name?: string;
        number_of_rooms?: number;
        occupancy_type_id?: string;
        occupancy_type_name?: string;
        meal_plan_id?: string;
        meal_plan_name?: string;
        [key: string]: any;
      };
      [key: string]: any;
    }[];
    [key: string]: any;
  };
  [key: string]: any;
}

export interface AdvancePaymentRequest {
  payment_mode: string;
  advance_amount: number;
  metadata?: any;
}

export interface AdvancePaymentResponse {
  success: boolean;
  payment_collection_id: string;
  cart_id: string;
  payment_mode: string;
  advance_amount: number;
  remaining_amount: number;
  currency_code: string;
  status: string;
  payment_collection_amount: number;
  message: string;
  manual?: {
    collection_method: string;
    status: string;
    next_action: string;
  };
  next_steps?: string[];
  [key: string]: any;
}

export interface RecordPaymentRequest {
  payment_mode: "manual";
  payment_collection_id: string;
  force_convert_to_advance: boolean;
  manual_payment: {
    payment_method: string;
    reference_number: string;
    collected_by: string;
    collection_date: string; // ISO format
    notes?: string;
    amount_received: number;
  };
  metadata?: {
    verification_status?: string;
    bank_name?: string;
    [key: string]: any;
  };
}

export interface RecordPaymentResponse {
  success: boolean;
  message: string;
  payment_collection_id: string;
  cart_id: string;
  payment_status: string;
  recorded_amount: number;
  currency_code: string;
  manual_payment: {
    payment_method: string;
    reference_number: string;
    collected_by: string;
    collection_date: string;
    amount_received: number;
    notes?: string;
  };
  metadata?: {
    verification_status?: string;
    bank_name?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface CartCompleteResponse {
  type: "cart" | "order";
  cart?: {
    id: string;
    status: string;
    [key: string]: any;
  };
  order?: {
    id: string;
    status: string;
    total: number;
    currency_code: string;
    payment_status: string;
    [key: string]: any;
  };
  error?: string;
  [key: string]: any;
}

export interface CartMetadataUpdateRequest {
  metadata: {
    destination_id?: string;
    destination_name?: string;
    hotel_id?: string;
    hotel_name?: string;
    check_in_date?: string;
    check_out_date?: string;
    traveller_info?: {
      primary_contact: {
        name: string;
        email: string;
        dob?: string;
        country?: string;
      };
      travellers: Array<{
        name: string;
        dob: string;
      }>;
    };
    [key: string]: any;
  };
}

export interface CartMetadataUpdateResponse {
  cart: {
    id: string;
    metadata?: any;
    [key: string]: any;
  };
  [key: string]: any;
}

// ============================================================================
// Main FlinkkInventoryAPI Class
// ============================================================================

export class FlinkkInventoryAPI {
  private apiUrl: string;
  private token: string;

  constructor(config: FlinkkInventoryAPIConfig) {
    // Validate required configuration
    if (!config.apiUrl) {
      throw new Error("FlinkkInventoryAPI: apiUrl is required");
    }
    if (!config.token) {
      throw new Error("FlinkkInventoryAPI: token is required");
    }

    this.apiUrl = config.apiUrl.replace(/\/$/, ""); // Remove trailing slash
    this.token = config.token;
  }

  // ============================================================================
  // Private Helper Methods
  // ============================================================================

  private async makeRequest<T = any>(
    endpoint: string,
    options: RequestInit = {},
    usePublishableKey: boolean = false,
  ): Promise<T> {
    const url = `${this.apiUrl}${endpoint}`;

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...(usePublishableKey
        ? { "x-publishable-api-key": this.token }
        : { Authorization: `Bearer ${this.token}` }),
      ...((options.headers as Record<string, string>) || {}),
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch (e) {
          errorData = { error: errorText };
        }
        console.error(`Inventory API error (${response.status}):`, errorData);
        throw new Error(
          `Inventory API error: ${response.status} ${response.statusText}${
            errorData.error ? ` - ${errorData.error}` : ""
          }`,
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Network error: ${String(error)}`);
    }
  }

  // ============================================================================
  // Public API Methods
  // ============================================================================

  /**
   * Verify connection to the inventory service
   *
   * Makes a GET request to /api/inventory/verify to check if the API URL
   * and authentication token are valid.
   *
   * @returns Promise<VerifyConnectionResponse> Connection status and details
   * @throws Error if connection fails or authentication is invalid
   */
  async verifyConnection(): Promise<VerifyConnectionResponse> {
    const response = await this.makeRequest<
      APIResponse<VerifyConnectionResponse>
    >("/api/inventory/verify", {
      method: "GET",
    });

    // Handle different response structures
    if (response.success && response.data) {
      return response.data;
    }

    // If the response doesn't follow APIResponse structure, check if it's a direct response
    if ((response as any).status) {
      return response as unknown as VerifyConnectionResponse;
    }

    // Fallback for unexpected response structure
    return {
      status: "connected",
      message: "Connection verified successfully",
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get products/services from the inventory
   *
   * @param params Query parameters for filtering products/services
   * @returns Promise<ProductService[]> Array of products/services
   */
  async getProductsServices(
    params: GetProductsServicesParams = {},
  ): Promise<ProductService[]> {
    const queryParams = new URLSearchParams();

    if (params.category_id) {
      queryParams.append("category_id", params.category_id);
    }
    if (params.search) {
      queryParams.append("search", params.search);
    }
    if (params.limit) {
      queryParams.append("limit", params.limit.toString());
    }
    if (params.page) {
      queryParams.append("page", params.page.toString());
    }

    const endpoint = `/store/products-services${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

    return await this.makeRequest<ProductService[]>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  /**
   * Get destinations from the inventory
   *
   * @param params Query parameters for filtering destinations
   * @returns Promise<{destinations: Destination[]}> Object containing array of destinations
   */
  async getDestinations(
    params: GetDestinationsParams = {},
  ): Promise<{ destinations: Destination[] }> {
    const queryParams = new URLSearchParams();

    if (params.search) {
      queryParams.append("search", params.search);
    }
    if (params.limit) {
      queryParams.append("limit", params.limit.toString());
    }
    if (params.page) {
      queryParams.append("page", params.page.toString());
    }

    const endpoint = `/store/hotel-management/destinations${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

    return await this.makeRequest<{ destinations: Destination[] }>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  /**
   * Get hotels from the inventory
   *
   * @param params Query parameters for filtering hotels
   * @returns Promise<{hotels: Hotel[]}> Object containing array of hotels
   */
  async getHotels(params: GetHotelsParams = {}): Promise<{ hotels: Hotel[] }> {
    const queryParams = new URLSearchParams();

    if (params.search) {
      queryParams.append("search", params.search);
    }
    if (params.destination_id) {
      queryParams.append("destination_id", params.destination_id);
    }
    if (params.limit) {
      queryParams.append("limit", params.limit.toString());
    }
    if (params.page) {
      queryParams.append("page", params.page.toString());
    }

    const endpoint = `/store/hotel-management/hotels${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

    return await this.makeRequest<{ hotels: Hotel[] }>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  /**
   * Get hotel room availability
   *
   * @param params Hotel availability parameters
   * @returns Promise<HotelAvailabilityResponse> Room availability data
   */
  async getHotelAvailability(params: {
    hotel_id: string;
    start_date: string;
    end_date: string;
    consolidate?: boolean;
  }): Promise<HotelAvailabilityResponse> {
    const queryParams = new URLSearchParams();

    queryParams.append("hotel_id", params.hotel_id);
    queryParams.append("start_date", params.start_date);
    queryParams.append("end_date", params.end_date);

    if (params.consolidate !== undefined) {
      queryParams.append("consolidate", params.consolidate.toString());
    }

    const endpoint = `/store/hotel-management/availability?${queryParams.toString()}`;

    return await this.makeRequest<HotelAvailabilityResponse>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  /**
   * Get hotel room pricing
   *
   * @param params Hotel pricing parameters
   * @returns Promise<HotelPricingResponse> Room pricing data
   */
  async getHotelPricing(params: {
    hotel_id: string;
    currency?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<HotelPricingResponse> {
    const queryParams = new URLSearchParams();

    if (params.currency) {
      queryParams.append("currency", params.currency);
    }

    if (params.start_date) {
      queryParams.append("start_date", params.start_date);
    }

    if (params.end_date) {
      queryParams.append("end_date", params.end_date);
    }

    const endpoint = `/store/hotel-management/hotels/${params.hotel_id}/pricing?${queryParams.toString()}`;

    return await this.makeRequest<HotelPricingResponse>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  /**
   * Get hotel occupancy configuration
   *
   * @param params Hotel occupancy config parameters
   * @returns Promise<{ occupancy_configs: OccupancyConfig[] }> Occupancy configuration data
   */
  async getHotelOccupancyConfig(params: {
    hotel_id: string;
  }): Promise<{ occupancy_configs: OccupancyConfig[] }> {
    const endpoint = `/store/hotel-management/hotels/${params.hotel_id}/pricing/occupancy-config`;

    return await this.makeRequest<{ occupancy_configs: OccupancyConfig[] }>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  /**
   * Get supplier offerings from the inventory
   *
   * @param params Query parameters for filtering supplier offerings
   * @returns Promise<SupplierOffering[]> Array of supplier offerings
   */
  async getSupplierOfferings(
    params: GetSupplierOfferingsParams,
  ): Promise<SupplierOffering[]> {
    const queryParams = new URLSearchParams();

    queryParams.append("product_service_id", params.product_service_id);

    if (params.start_date) {
      queryParams.append("start_date", params.start_date);
    }
    if (params.end_date) {
      queryParams.append("end_date", params.end_date);
    }
    if (params.limit) {
      queryParams.append("limit", params.limit.toString());
    }
    if (params.page) {
      queryParams.append("page", params.page.toString());
    }

    const endpoint = `/store/supplier-offerings?${queryParams.toString()}`;

    return await this.makeRequest<SupplierOffering[]>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  /**
   * Get addon categories from the inventory
   *
   * @param params Query parameters for filtering addon categories
   * @returns Promise<AddonCategory[]> Array of addon categories
   */
  async getAddonCategories(
    params: GetAddonCategoriesParams = {},
  ): Promise<AddonCategory[]> {
    const queryParams = new URLSearchParams();

    if (params.is_active !== undefined) {
      queryParams.append("is_active", params.is_active.toString());
    }
    if (params.limit) {
      queryParams.append("limit", params.limit.toString());
    }
    if (params.sort_by) {
      queryParams.append("sort_by", params.sort_by);
    }
    if (params.sort_order) {
      queryParams.append("sort_order", params.sort_order);
    }

    const endpoint = `/store/products-services/categories${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

    return await this.makeRequest<AddonCategory[]>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  // ============================================================================
  // Customer Management Methods
  // ============================================================================

  /**
   * Check if a customer exists in the inventory by email
   *
   * @param email Customer email to check
   * @returns Promise<CustomerCheckResponse> Whether customer exists and customer data if found
   */
  async checkCustomerExists(email: string): Promise<CustomerCheckResponse> {
    const queryParams = new URLSearchParams();
    queryParams.append("email", email);

    const endpoint = `/store/customers/check?${queryParams.toString()}`;

    return await this.makeRequest<CustomerCheckResponse>(
      endpoint,
      { method: "GET" },
      true, // Use publishable key
    );
  }

  /**
   * Register a new customer in the inventory system
   *
   * @param customerData Customer registration data
   * @returns Promise<CustomerRegisterResponse> Registration response with token
   */
  async registerCustomer(
    customerData: CustomerRegisterRequest,
  ): Promise<CustomerRegisterResponse> {
    return await this.makeRequest<CustomerRegisterResponse>(
      "/auth/customer/emailpass/register",
      {
        method: "POST",
        body: JSON.stringify(customerData),
      },
      true, // Use publishable key
    );
  }

  /**
   * Create a customer in the inventory system
   *
   * @param customerData Customer creation data
   * @param authToken Authorization token from registration
   * @returns Promise<CustomerCreateResponse> Created customer data
   */
  async createCustomer(
    customerData: CustomerCreateRequest,
    authToken: string,
  ): Promise<CustomerCreateResponse> {
    console.log({ customerData, authToken });
    return await this.makeRequest<CustomerCreateResponse>(
      "/store/customers",
      {
        method: "POST",
        body: JSON.stringify(customerData),
        headers: {
          Authorization: `Bearer ${authToken}`,
          "x-publishable-api-key": this.token, // Include both headers as required
        },
      },
      false, // Don't use publishable key mode since we're setting headers manually
    );
  }

  // ============================================================================
  // Cart Management Methods
  // ============================================================================

  /**
   * Create a cart in the inventory system
   *
   * @param cartData Cart creation data
   * @returns Promise<CartCreateResponse> Created cart data
   */
  async createCart(cartData: CartCreateRequest): Promise<CartCreateResponse> {
    console.log("Creating cart with data:", cartData);
    return await this.makeRequest<CartCreateResponse>(
      "/store/carts",
      {
        method: "POST",
        body: JSON.stringify(cartData),
      },
      true, // Use publishable key
    );
  }

  /**
   * Add items to a cart in the inventory system
   *
   * @param cartId Cart ID to add items to
   * @param items Array of items to add to the cart
   * @returns Promise<AddItemsResponse> Updated cart data with new items
   */
  async addItemsToCart(
    cartId: string,
    items: CartItem[],
  ): Promise<AddItemsResponse> {
    console.log("Adding items to cart:", cartId, items);
    const payload: AddItemsRequest = { items };
    return await this.makeRequest<AddItemsResponse>(
      `/store/carts/${cartId}/add-items`,
      {
        method: "POST",
        body: JSON.stringify(payload),
      },
      true, // Use publishable key
    );
  }

  /**
   * Get cart data from the inventory system
   *
   * @param cartId Cart ID to retrieve
   * @returns Promise<CartResponse> Cart data with items
   */
  async getCart(cartId: string): Promise<CartResponse> {
    console.log("Getting cart:", cartId);
    return await this.makeRequest<CartResponse>(
      `/store/carts/${cartId}`,
      {
        method: "GET",
      },
      true, // Use publishable key
    );
  }

  /**
   * Convert a cart to an order in the inventory system
   *
   * @param cartId Cart ID to convert to order
   * @param conversionData Order conversion data including payment details
   * @returns Promise<CartConvertToOrderResponse> Created order data
   */
  async convertCartToOrder(
    cartId: string,
    conversionData: CartConvertToOrderRequest,
  ): Promise<CartConvertToOrderResponse> {
    console.log("Converting cart to order:", cartId, conversionData);
    return await this.makeRequest<CartConvertToOrderResponse>(
      `/store/carts/${cartId}/convert-to-order`,
      {
        method: "POST",
        body: JSON.stringify(conversionData),
      },
      true, // Use publishable key
    );
  }

  /**
   * Initiate advance payment for a cart in the inventory system
   *
   * @param cartId Cart ID to initiate advance payment for
   * @param paymentData Advance payment data including amount
   * @returns Promise<AdvancePaymentResponse> Advance payment response with payment collection ID
   */
  async initiateAdvancePayment(
    cartId: string,
    paymentData: AdvancePaymentRequest,
  ): Promise<AdvancePaymentResponse> {
    console.log("Initiating advance payment for cart:", cartId, paymentData);
    return await this.makeRequest<AdvancePaymentResponse>(
      `/store/carts/${cartId}/advance-payment`,
      {
        method: "POST",
        body: JSON.stringify(paymentData),
      },
      true, // Use publishable key
    );
  }

  /**
   * Record manual payment for a cart in the inventory system
   *
   * @param cartId Cart ID to record payment for
   * @param paymentData Manual payment recording data
   * @returns Promise<RecordPaymentResponse> Payment recording response
   */
  async recordPayment(
    cartId: string,
    paymentData: RecordPaymentRequest,
  ): Promise<RecordPaymentResponse> {
    console.log("Recording payment for cart:", cartId, paymentData);
    return await this.makeRequest<RecordPaymentResponse>(
      `/store/carts/${cartId}/record-payment`,
      {
        method: "POST",
        body: JSON.stringify(paymentData),
      },
      true, // Use publishable key
    );
  }

  /**
   * Complete a cart in the inventory system
   *
   * @param cartId Cart ID to complete
   * @returns Promise<CartCompleteResponse> Cart completion response
   */
  async completeCart(cartId: string): Promise<CartCompleteResponse> {
    console.log("Completing cart:", cartId);
    return await this.makeRequest<CartCompleteResponse>(
      `/store/carts/${cartId}/complete`,
      {
        method: "POST",
      },
      true, // Use publishable key
    );
  }

  /**
   * Update cart metadata in the inventory system
   *
   * @param cartId Cart ID to update
   * @param metadataUpdateData Metadata update data
   * @returns Promise<CartMetadataUpdateResponse> Updated cart data
   */
  async updateCartMetadata(
    cartId: string,
    metadataUpdateData: CartMetadataUpdateRequest,
  ): Promise<CartMetadataUpdateResponse> {
    console.log("Updating cart metadata:", cartId, metadataUpdateData);
    return await this.makeRequest<CartMetadataUpdateResponse>(
      `/store/carts/${cartId}`,
      {
        method: "POST",
        body: JSON.stringify(metadataUpdateData),
      },
      true, // Use publishable key
    );
  }

  // ============================================================================
  // Future Methods (Placeholder for extensibility)
  // ============================================================================

  /**
   * Sync stock levels for products (Future implementation)
   *
   * @param stockData Array of stock level updates
   * @returns Promise<APIResponse> Update results
   */
  // async syncStock(stockData: StockUpdate[]): Promise<APIResponse> {
  //   // Future implementation
  //   throw new Error("syncStock method not yet implemented");
  // }

  /**
   * Get product availability (Future implementation)
   *
   * @param productIds Array of product IDs to check
   * @returns Promise<ProductAvailability[]> Availability data
   */
  // async getAvailability(productIds: string[]): Promise<ProductAvailability[]> {
  //   // Future implementation
  //   throw new Error("getAvailability method not yet implemented");
  // }
}

// ============================================================================
// Factory Function (Optional convenience)
// ============================================================================

/**
 * Create a new FlinkkInventoryAPI instance
 *
 * @param config Configuration object with apiUrl and token
 * @returns FlinkkInventoryAPI instance
 */
export function createFlinkkInventoryAPI(
  config: FlinkkInventoryAPIConfig,
): FlinkkInventoryAPI {
  return new FlinkkInventoryAPI(config);
}

// ============================================================================
// Default Export
// ============================================================================

export default FlinkkInventoryAPI;
