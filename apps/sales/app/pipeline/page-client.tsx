"use client";

import React, { useState, useTransition, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import toast from "react-hot-toast";
import { Button } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import { Badge } from "@flinkk/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import { PlusIcon, SearchIcon } from "lucide-react";
import { FlinkkClient } from "@flinkk/services";
import type { Opportunity, PipelineStage } from "./page";
import { OpportunityCard } from "./_components/opportunity-card";
import { Typography } from "@flinkk/components/ui/typography";

interface PipelineClientProps {
  initialData: PipelineStage[];
}

interface DynamicStage {
  value: string;
  label: string;
  description?: string;
  color?: string;
  icon?: string;
  order: number;
}

export function PipelineClient({ initialData }: PipelineClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const [pipeline, setPipeline] = useState<PipelineStage[]>(initialData);
  const [searchQuery, setSearchQuery] = useState(
    searchParams.get("search") || "",
  );
  const [userFilter, setUserFilter] = useState<string | null>(
    searchParams.get("userId") || null,
  );
  const [accountFilter, setAccountFilter] = useState<string | null>(
    searchParams.get("accountId") || null,
  );
  const [isDragging, setIsDragging] = useState(false);
  const [draggedOpportunity, setDraggedOpportunity] =
    useState<Opportunity | null>(null);
  const [stageColors, setStageColors] = useState<Record<string, string>>({});

  // Fetch dynamic stage colors
  useEffect(() => {
    const fetchStageColors = async () => {
      try {
        const response = await fetch("/api/dynamic-select/opportunity-stage", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const stages: DynamicStage[] = await response.json();
          const colors: Record<string, string> = {};

          stages.forEach((stage) => {
            // Extract color classes from the color string
            if (stage.color) {
              // Convert color classes to a simple color for the pipeline
              if (stage.color.includes("blue")) {
                colors[stage.value] = "bg-blue-500";
              } else if (stage.color.includes("purple")) {
                colors[stage.value] = "bg-purple-500";
              } else if (stage.color.includes("yellow")) {
                colors[stage.value] = "bg-yellow-500";
              } else if (stage.color.includes("orange")) {
                colors[stage.value] = "bg-orange-500";
              } else if (stage.color.includes("green")) {
                colors[stage.value] = "bg-green-500";
              } else if (stage.color.includes("red")) {
                colors[stage.value] = "bg-red-500";
              } else {
                colors[stage.value] = "bg-gray-500";
              }
            } else {
              colors[stage.value] = "bg-gray-500";
            }
          });

          setStageColors(colors);
        }
      } catch (error) {
        console.error("Error fetching stage colors:", error);
      }
    };

    fetchStageColors();
  }, []);

  // Update URL and refresh data
  const updateFilters = (
    newSearch?: string,
    newUserId?: string | null,
    newAccountId?: string | null,
  ) => {
    const params = new URLSearchParams();

    const search = newSearch !== undefined ? newSearch : searchQuery;
    const userId = newUserId !== undefined ? newUserId : userFilter;
    const accountId = newAccountId !== undefined ? newAccountId : accountFilter;

    if (search) params.set("search", search);
    if (userId) params.set("userId", userId);
    if (accountId) params.set("accountId", accountId);

    const newUrl = `/pipeline${params.toString() ? `?${params.toString()}` : ""}`;

    startTransition(() => {
      router.push(newUrl);
    });
  };

  // Handle search
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    updateFilters(value);
  };

  // Handle user filter change
  const handleUserFilterChange = (value: string) => {
    const newValue = value === "all" ? null : value;
    setUserFilter(newValue);
    updateFilters(undefined, newValue);
  };

  // Handle account filter change
  const handleAccountFilterChange = (value: string) => {
    const newValue = value === "all" ? null : value;
    setAccountFilter(newValue);
    updateFilters(undefined, undefined, newValue);
  };

  // Handle drag start
  const handleDragStart = (opportunity: Opportunity) => {
    setIsDragging(true);
    setDraggedOpportunity(opportunity);
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  // Handle drop
  const handleDrop = async (
    e: React.DragEvent<HTMLDivElement>,
    newStage: string,
  ) => {
    e.preventDefault();
    setIsDragging(false);

    if (!draggedOpportunity || draggedOpportunity.stage === newStage) {
      return;
    }

    try {
      // Use FlinkkClient.update instead of server action
      await FlinkkClient.update("opportunity", draggedOpportunity.id, {
        stage: newStage,
      });

      toast.success(
        `Opportunity moved to ${newStage.replace("_", " ").toLowerCase()}`,
      );

      // Update local state optimistically
      setPipeline((prevPipeline) => {
        return prevPipeline.map((stage) => ({
          ...stage,
          opportunities: stage.opportunities
            .filter((opp) => opp.id !== draggedOpportunity.id)
            .concat(
              stage.stage === newStage
                ? [{ ...draggedOpportunity, stage: newStage }]
                : [],
            ),
        }));
      });

      // Refresh the page to get updated data
      startTransition(() => {
        router.refresh();
      });
    } catch (error) {
      console.error("Error updating opportunity stage:", error);
      toast.error("Failed to update opportunity stage. Please try again.");
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-col md:flex-row gap-2 md:items-center ">
          <div className="relative flex-1 max-w-sm">
            <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search opportunities..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              disabled={isPending}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Select
              value={userFilter || "all"}
              onValueChange={handleUserFilterChange}
              disabled={isPending}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by owner" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Owners</SelectItem>
                <SelectItem value="user1">John Doe</SelectItem>
                <SelectItem value="user2">Jane Smith</SelectItem>
                <SelectItem value="user3">Bob Johnson</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={accountFilter || "all"}
              onValueChange={handleAccountFilterChange}
              disabled={isPending}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by account" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Accounts</SelectItem>
                <SelectItem value="account1">Acme Corporation</SelectItem>
                <SelectItem value="account2">Globex Inc</SelectItem>
                <SelectItem value="account3">Initech LLC</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button onClick={() => router.push("/opportunities/new")}>
          <PlusIcon className="h-4 w-4 mr-2" />
          New Opportunity
        </Button>
      </div>

      {isPending && (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Loading...</div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-2">
        {pipeline.map((stage) => (
          <div
            key={stage.stage}
            className="flex flex-col h-full"
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, stage.stage)}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div
                  className={`w-3 h-3 rounded-full ${stageColors[stage.stage] || "bg-gray-500"} mr-2`}
                ></div>
                <h3 className="font-medium">{stage.label}</h3>
              </div>
              <Badge variant="outline">{stage.opportunities.length}</Badge>
            </div>

            <div className="flex-1 bg-muted/30 rounded-lg p-2 min-h-[500px] overflow-y-auto">
              {stage.opportunities.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground p-4">
                  {/* <p>No opportunities in this stage</p> */}
                </div>
              ) : (
                <div className="space-y-2">
                  {stage.opportunities.map((opportunity) => (
                    <OpportunityCard
                      opportunity={opportunity}
                      stage={stage}
                      isDragging={isDragging}
                      setIsDragging={setIsDragging}
                      handleDrop={handleDrop}
                      handleDragStart={handleDragStart}
                      draggedOpportunity={draggedOpportunity}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
