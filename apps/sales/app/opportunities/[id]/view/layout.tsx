import { getOpportunityById } from "./actions";
import { Header } from "./_components/header";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@flinkk/components/ui/resizable";
import { OpportunityStage } from "./_components/opportunity-stage";
import { getStatusOptions } from "@/lib/actions/status-options";
import {
  Ta<PERSON>,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@flinkk/components/ui/tabs";
import { LiveNotepadPanel } from "@flinkk/live-notepad";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { CustomSession } from "@flinkk/shared-auth/options";

export default async function FormPageLayout({
  children,
  tasks,
  activity,
  notes,
  email,
  whatsapp,
  params,
}: {
  children: React.ReactNode;
  tasks: React.ReactNode;
  activity: React.ReactNode;
  notes: React.ReactNode;
  email: React.ReactNode;
  whatsapp: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const session = (await getServerSession()) as CustomSession;
  const [opportunityDetails, statusOptionsData] = await Promise.all([
    getOpportunityById(id),
    getStatusOptions("opportunity"),
  ]);

  return (
    <>
      <div className="w-full h-svh flex flex-col overflow-hidden">
        {/* Fixed Header Section */}
        <div className="flex-shrink-0 px-4">
          <Header
            opportunity={opportunityDetails}
            opportunityId={id}
            userId={opportunityDetails.userId}
            opportunityEmail={opportunityDetails.contact?.email}
          />
          <OpportunityStage
            stage={opportunityDetails.stage}
            status={opportunityDetails.status}
            id={id}
          />
        </div>

        <div className="flex-1 overflow-hidden pt-4">
          {/* Desktop Layout (lg and above) */}
          <div className="hidden lg:block h-full">
            <ResizablePanelGroup
              direction="horizontal"
              className="h-full w-full px-2 sm:px-4 space-x-1 sm:space-x-1.5"
            >
              {/* Left Column (25%): Opportunity Information Cards */}
              <ResizablePanel defaultSize={25} minSize={20} maxSize={30}>
                <div className="h-full overflow-y-auto scrollbar-none">
                  {children}
                </div>
              </ResizablePanel>

              <ResizableHandle withHandle className="invisible" />

              {/* Center Column (50%): Live Notepad + Tabbed Interface */}
              <ResizablePanel defaultSize={50} minSize={40} maxSize={60}>
                <div className="h-full overflow-hidden space-y-4">
                  <div className="h-full flex flex-col overflow-scroll">
                    {/* Live Notepad Panel - Always visible above tabs */}
                    <div className="flex-shrink-0 mb-4">
                      <LiveNotepadPanel
                        entityId={id}
                        entityType="Opportunity"
                        userId={session?.userId}
                      />
                    </div>

                    <Tabs
                      defaultValue="notes"
                      className="w-full h-full flex flex-col"
                    >
                      {/* Fixed Tab Headers */}
                      <div className="flex-shrink-0">
                        <TabsList className="grid w-full grid-cols-4 mb-3">
                          <TabsTrigger
                            value="notes"
                            className="text-xs sm:text-sm"
                          >
                            Notes
                          </TabsTrigger>
                          <TabsTrigger
                            value="emails"
                            className="text-xs sm:text-sm"
                          >
                            Emails
                          </TabsTrigger>
                          <TabsTrigger
                            value="tasks"
                            className="text-xs sm:text-sm"
                          >
                            Tasks
                          </TabsTrigger>
                          <TabsTrigger
                            value="whatsapp"
                            className="text-xs sm:text-sm"
                          >
                            WhatsApp
                          </TabsTrigger>
                        </TabsList>
                      </div>

                      {/* Scrollable Tab Content */}
                      <div className="flex-1 overflow-hidden">
                        {/* Notes Tab */}
                        <TabsContent
                          value="notes"
                          className="h-full overflow-y-auto scrollbar-none mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                        >
                          <div className="flex-1 overflow-y-auto scrollbar-none">
                            {notes}
                          </div>
                        </TabsContent>

                        {/* Emails Tab */}
                        <TabsContent
                          value="emails"
                          className="h-full overflow-y-auto scrollbar-none mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                        >
                          <div className="flex-1 overflow-y-auto scrollbar-none">
                            {email}
                          </div>
                        </TabsContent>

                        {/* Tasks Tab */}
                        <TabsContent
                          value="tasks"
                          className="h-full overflow-y-auto scrollbar-none mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                        >
                          <div className="flex-1 overflow-y-auto scrollbar-none">
                            {tasks}
                          </div>
                        </TabsContent>

                        {/* WhatsApp Tab */}
                        <TabsContent
                          value="whatsapp"
                          className="h-full overflow-y-auto scrollbar-none mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                        >
                          <div className="flex-1 overflow-y-auto scrollbar-none">
                            {whatsapp}
                          </div>
                        </TabsContent>
                      </div>
                    </Tabs>
                  </div>
                </div>
              </ResizablePanel>

              <ResizableHandle withHandle className="invisible" />

              {/* Right Column (25%): Assignment & Status, Opportunity Qualification, Quote, Activity Log */}
              <ResizablePanel defaultSize={25} minSize={20} maxSize={30}>
                <div className="h-full overflow-y-auto scrollbar-none space-y-4">
                  {activity}
                </div>
              </ResizablePanel>
            </ResizablePanelGroup>
          </div>

          {/* Mobile/Tablet Layout (below lg) */}
          <div className="lg:hidden h-full overflow-y-auto px-4">
            <Tabs defaultValue="information" className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-4 sticky top-0 z-10 bg-background">
                <TabsTrigger value="information" className="text-xs">
                  Info
                </TabsTrigger>
                <TabsTrigger value="activity" className="text-xs">
                  Activity
                </TabsTrigger>
                <TabsTrigger value="communication" className="text-xs">
                  Comm
                </TabsTrigger>
                <TabsTrigger value="notes" className="text-xs">
                  Notes
                </TabsTrigger>
              </TabsList>

              {/* Information Tab - Opportunity Information Cards */}
              <TabsContent value="information" className="mt-0">
                <div className="space-y-4">{children}</div>
              </TabsContent>

              {/* Activity Tab - Assignment & Status, Opportunity Qualification, etc. */}
              <TabsContent value="activity" className="mt-0">
                <div className="space-y-4">{activity}</div>
              </TabsContent>

              {/* Communication Tab - Emails, Tasks, WhatsApp */}
              <TabsContent value="communication" className="mt-0">
                <div className="space-y-4">
                  <Tabs defaultValue="emails" className="w-full">
                    <TabsList className="grid w-full grid-cols-3 mb-4">
                      <TabsTrigger value="emails" className="text-xs">
                        Emails
                      </TabsTrigger>
                      <TabsTrigger value="tasks" className="text-xs">
                        Tasks
                      </TabsTrigger>
                      <TabsTrigger value="whatsapp" className="text-xs">
                        WhatsApp
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="emails" className="mt-0">
                      {email}
                    </TabsContent>
                    <TabsContent value="tasks" className="mt-0">
                      {tasks}
                    </TabsContent>
                    <TabsContent value="whatsapp" className="mt-0">
                      {whatsapp}
                    </TabsContent>
                  </Tabs>
                </div>
              </TabsContent>

              {/* Notes Tab - Live Notepad + Notes */}
              <TabsContent value="notes" className="mt-0">
                <div className="space-y-4">
                  <LiveNotepadPanel
                    entityId={id}
                    entityType="Opportunity"
                    userId={session?.userId}
                  />
                  {notes}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </>
  );
}
