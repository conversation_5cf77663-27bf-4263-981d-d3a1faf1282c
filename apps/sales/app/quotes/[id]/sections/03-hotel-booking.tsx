"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Calendar } from "lucide-react";
import { Control } from "react-hook-form";
import {
  DynamicSelectFieldFormElement,
} from "@flinkk/dynamic-form/form-elements";
import {
  OccupancyFields,
  transformOccupancyToGuestDetails,
  hasOccupancyData
} from "../../../../../../packages/components/forms/occupancy-fields";
import { useOccupancyConfig } from "../../../../../../packages/hooks/query/use-occupancy-config";
import { HotelDateRangePicker } from "../_components/hotel-date-range-picker";
import {
  HotelBookingCalendar,
  RoomBlock,
} from "../_components/hotel-booking-calendar";
import { SavedRoomBookings } from "../_components/cart-items-display";

type CapacityValidation = { errors: string[]; warnings: string[] };

type Props = {
  control: Control<any>;
  selectedDestinationData: any;
  setSelectedDestinationData: (d: any) => void;
  selectedHotelData: any;
  setSelectedHotelData: (d: any) => void;

  hotelRoomBlocks: RoomBlock[];
  setHotelRoomBlocks: (blocks: RoomBlock[]) => void;

  showAvailabilityCheck: boolean;
  setShowAvailabilityCheck: (v: boolean) => void;

  hasSavedRoomBookings: () => boolean;
  handleCheckAvailability: () => void;
  handleAddRoomBookingsToQuotation: (
    roomBlocks: RoomBlock[],
    hotelData?: any,
  ) => Promise<void>;

  capacityValidation: CapacityValidation;
  isSavingRoomBookings: boolean;

  // Cart details for displaying saved room bookings
  cartDetails?: any;
  formatCurrency?: (amount: number) => string;
};

export default function HotelBookingSection({
  control,
  selectedDestinationData,
  setSelectedDestinationData,
  selectedHotelData,
  setSelectedHotelData,
  hotelRoomBlocks,
  setHotelRoomBlocks,
  showAvailabilityCheck,
  setShowAvailabilityCheck,
  hasSavedRoomBookings,
  handleCheckAvailability,
  handleAddRoomBookingsToQuotation,
  capacityValidation,
  isSavingRoomBookings,
  cartDetails,
  formatCurrency,
}: Props) {
  // Get current form values
  const formValues = (control as any)._formValues || {};
  const selectedHotelId = formValues.selectedHotel;

  // Fetch occupancy configs for the selected hotel
  const {
    data: occupancyData,
  } = useOccupancyConfig({
    hotelId: selectedHotelId,
    enabled: !!selectedHotelId,
  });

  const occupancyConfigs = (occupancyData as any)?.occupancy_configs || [];

  // Transform occupancy data to guest details format
  const guestDetails = transformOccupancyToGuestDetails(
    formValues,
    occupancyConfigs,
    "occupancy_"
  );

  // Check if occupancy data is available
  const hasOccupancy = hasOccupancyData(
    formValues,
    occupancyConfigs,
    "occupancy_"
  );
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Hotel Booking</CardTitle>
            <CardDescription>
              {hasSavedRoomBookings()
                ? "Manage your hotel bookings and check availability"
                : "Select hotel and room availability for this quotation"}
            </CardDescription>
          </div>
          {hasSavedRoomBookings() && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleCheckAvailability}
              className="flex items-center gap-2"
            >
              <Calendar className="h-4 w-4" />
              Check Availability
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="mt-2">
        {/* Saved Room Bookings - Show when there are saved bookings */}
        {hasSavedRoomBookings() && (
          <SavedRoomBookings
            cartDetails={cartDetails}
            formatCurrency={formatCurrency}
            className="mb-6"
          />
        )}

        <div className="space-y-6">
          {/* Step 1: Destination and Hotel Selection */}
          {(!hasSavedRoomBookings() || showAvailabilityCheck) && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <h3 className="text-lg font-medium">
                  Select Destination & Hotel
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <DynamicSelectFieldFormElement
                  name="selectedDestination"
                  label="Destination"
                  control={control}
                  placeholder="Select a destination first"
                  dynamicKey="destinations"
                  required={true}
                  helperText="Select a destination to see available hotels"
                  onSelectionChange={(selectedOption) => {
                    setSelectedDestinationData(selectedOption);
                    // Reset downstream selections
                    (control as any)._defaultValues["selectedHotel"] = "";
                    (control as any)._defaultValues["checkInDate"] = "";
                    (control as any)._defaultValues["checkOutDate"] = "";
                    (control as any)._defaultValues["adults"] = 1;
                    (control as any)._defaultValues["children"] = 0;
                    setSelectedHotelData(null);
                  }}
                />

                {/* Hotel select depends on selectedDestination; we keep the same UX */}
                <DynamicSelectFieldFormElement
                  name="selectedHotel"
                  label="Hotel"
                  control={control}
                  placeholder="Search and select a hotel"
                  dynamicKey="hotels"
                  payload={{
                    destination_id: (control as any)._formValues
                      ?.selectedDestination,
                  }}
                  onSelectionChange={(selectedOption) => {
                    setSelectedHotelData(selectedOption);
                    (control as any)._defaultValues["checkInDate"] = "";
                    (control as any)._defaultValues["checkOutDate"] = "";
                    // Clear legacy fields
                    (control as any)._defaultValues["adults"] = undefined;
                    (control as any)._defaultValues["children"] = undefined;
                    // Note: Dynamic occupancy fields will be cleared automatically when hotel changes
                  }}
                  helperText="Hotels are filtered based on the selected destination"
                />
              </div>
            </div>
          )}

          {/* Step 2: Date Range Selection */}
          {(!hasSavedRoomBookings() || showAvailabilityCheck) &&
            (control as any)._formValues?.selectedDestination &&
            (control as any)._formValues?.selectedHotel && (
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    2
                  </div>
                  <h3 className="text-lg font-medium">Travel Dates</h3>
                </div>
                <HotelDateRangePicker control={control} />
              </div>
            )}

          {/* Step 3: Guest Details */}
          {(!hasSavedRoomBookings() || showAvailabilityCheck) &&
            (control as any)._formValues?.selectedDestination &&
            (control as any)._formValues?.selectedHotel &&
            (control as any)._formValues?.checkInDate &&
            (control as any)._formValues?.checkOutDate && (
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    3
                  </div>
                  <h3 className="text-lg font-medium">Occupancy Details</h3>
                </div>

                {/* Dynamic Occupancy Fields */}
                <OccupancyFields
                  hotelId={(control as any)._formValues?.selectedHotel}
                  control={control}
                  fieldNamePrefix="occupancy_"
                  disabled={false}
                  hideBaseFields={true}
                />

                {/* Capacity warnings/errors rendering kept minimal; upstream page already handles messages */}
                {capacityValidation.errors.length > 0 && (
                  <div className="text-sm text-red-600">
                    {capacityValidation.errors.join(", ")}
                  </div>
                )}
                {capacityValidation.warnings.length > 0 && (
                  <div className="text-sm text-yellow-600">
                    {capacityValidation.warnings.join(", ")}
                  </div>
                )}
              </div>
            )}

          {/* Step 4: Room Availability Grid */}
          {(!hasSavedRoomBookings() || showAvailabilityCheck) &&
            formValues?.selectedDestination &&
            formValues?.selectedHotel &&
            formValues?.checkInDate &&
            formValues?.checkOutDate &&
            (hasOccupancy || guestDetails.adults >= 1) && (
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    4
                  </div>
                  <h3 className="text-lg font-medium">Room Availability</h3>
                </div>
                <HotelBookingCalendar
                  selectedHotel={formValues?.selectedHotel}
                  guestDetails={guestDetails}
                  initialRoomBlocks={hotelRoomBlocks}
                  onRoomBlocksChange={setHotelRoomBlocks}
                  onAddToQuotation={handleAddRoomBookingsToQuotation}
                  isLoadingSave={isSavingRoomBookings}
                  className="mt-6"
                />
              </div>
            )}
        </div>
      </CardContent>
    </Card>
  );
}
