"use client";

import React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@flinkk/components/ui/badge";
import { Button } from "@flinkk/components/ui/button";
import { Checkbox } from "@flinkk/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@flinkk/components/ui/dropdown-menu";
import { DataTable } from "@flinkk/data-table/component/data-table";
import { DataTableColumnHeader } from "@flinkk/data-table/component/data-table-column-header";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { useDelete } from "@flinkk/hooks/mutation/use-delete";
import { MoreH<PERSON>zon<PERSON>Icon, FileTextIcon } from "lucide-react";
import { EnhancedQuoteGeneratorButton } from "@flinkk/quote-pdf";
import { useQuoteTemplate } from "@flinkk/shared-hooks/use-quote-template";
import { useQuery } from "@tanstack/react-query";

const statusOptions = [
  { label: "Draft", value: "DRAFT" },
  { label: "In Review", value: "IN_REVIEW" },
  { label: "Sent", value: "SENT" },
  { label: "Accepted", value: "ACCEPTED" },
  { label: "Rejected", value: "REJECTED" },
  { label: "Expired", value: "EXPIRED" },
  { label: "Converted", value: "CONVERTED" },
  { label: "Cancelled", value: "CANCELLED" },
];

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface QuotesTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

// Organization data hook
function useOrganizationData() {
  return useQuery({
    queryKey: ["organization-data"],
    queryFn: async () => {
      const response = await fetch("/api/quote-templates");
      if (!response.ok) {
        throw new Error("Failed to fetch organization data");
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function QuotesTable({
  data,
  pageCount,
  permissions,
}: QuotesTableProps) {
  console.log({ data });
  const router = useRouter();

  // Fetch organization's template preference
  const { templateId, templateName } = useQuoteTemplate();

  // Fetch organization data
  const { data: organizationData } = useOrganizationData();

  const { deleteRecord: deleteQuote } = useDelete({
    model: "quote",
  });

  // Handle row click navigation
  const handleRowClick = React.useCallback(
    (quote: any) => {
      // Use quote number for the tab title
      const quoteNumber =
        quote.quoteNumber || `Q-${quote.id.slice(-8).toUpperCase()}`;

      // Create URL with title parameter for view mode
      const url = `/quotes/${quote.id}?title=${encodeURIComponent(quoteNumber)}&mode=view`;
      router.push(url);
    },
    [router],
  );

  // Helper function to transform quote data for PDF generation
  const transformQuoteForPDF = (quote: any) => {
    return {
      quote_number: `QUO-${quote.id.slice(-8).toUpperCase()}`,
      date_issued: quote.createdAt,
      valid_until:
        quote.validUntil ||
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      customer_name: quote.contact
        ? `${quote.contact.firstName} ${quote.contact.lastName}`
        : quote.account?.name || "Customer",
      customer_address: quote.account?.address || "Customer Address",
      payment_terms: quote.paymentTerms || "Net 30",
      line_items:
        quote.lines?.map((line: any) => ({
          description: line.description,
          quantity: line.quantity,
          unit_price: line.unitPrice,
          subtotal: line.subtotal || 0,
          discount_type: line.discountType || "PERCENTAGE",
          discount_value: line.discountValue || 0,
          tax_rate: line.taxRate || 0,
          is_manual_pricing: line.isManualPricing || false,
          manual_line_total: line.manualLineTotal || undefined,
          item_type: "PRODUCT" as const,
          unit_type: line.unitType || undefined,
        })) || [],
      package_items: [], // No packages in list view
      // Enhanced pricing fields
      quote_discount_type: (quote.quoteDiscountType || "PERCENTAGE") as
        | "PERCENTAGE"
        | "FLAT",
      quote_discount_value: quote.quoteDiscountValue || 0,
      intra_state_tax_rate: quote.intraStateTaxRate || 0,
      inter_state_tax_rate: quote.interStateTaxRate || 0,
      adjustments: quote.adjustments || 0,
      is_manual_total: quote.isManualTotal || false,
      manual_total_value: quote.manualTotalValue || undefined,
      is_manual_grand_total: quote.isManualGrandTotal || false,
      manual_grand_total: quote.manualGrandTotal || undefined,
      // Enhanced quotation details
      quotation_from: quote.quotationFromBusinessName
        ? {
            business_name: quote.quotationFromBusinessName,
            address: quote.quotationFromAddress,
            city: quote.quotationFromCity,
            state: quote.quotationFromState,
            postal_code: quote.quotationFromPostalCode,
            country: quote.quotationFromCountry,
            gstin: quote.quotationFromGSTIN,
          }
        : undefined,
      quotation_to: quote.quotationToBusinessName
        ? {
            business_name: quote.quotationToBusinessName,
            address: quote.quotationToAddress,
            city: quote.quotationToCity,
            state: quote.quotationToState,
            postal_code: quote.quotationToPostalCode,
            country: quote.quotationToCountry,
            gstin: quote.quotationToGSTIN,
          }
        : undefined,
    };
  };

  const columns = React.useMemo<ColumnDef<any, unknown>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-0.5"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-0.5"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },

      {
        accessorKey: "name",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Quote Number" />
        ),
        cell: ({ row }) => (
          <div className="font-medium max-w-[200px] truncate">
            {row.getValue("name") || "No description"}
          </div>
        ),
        enableColumnFilter: true,
        meta: {
          variant: "text",
          label: "Search quotations",
          placeholder: "Search by quote number...",
        },
      },
      {
        accessorKey: "status",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }) => {
          const status = row.getValue("status") as string;
          const statusOption = statusOptions.find(
            (option) => option.value === status,
          );

          return (
            <Badge variant="outline">{statusOption?.label || status}</Badge>
          );
        },

        enableColumnFilter: true,
        meta: {
          variant: "select",
          label: "Status",
          options: statusOptions,
        },
      },
      {
        accessorKey: "grandTotal",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Total Amount" />
        ),
        cell: ({ row }) => {
          const amount = parseFloat(row.getValue("grandTotal"));
          const currency = row.original.currency || "USD";

          return (
            <div className="text-right font-medium">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: currency,
              }).format(amount)}
            </div>
          );
        },
      },
      {
        accessorKey: "validUntil",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Valid Until" />
        ),
        cell: ({ row }) => {
          const date = row.getValue("validUntil") as string;
          return date ? formatDate(date) : "—";
        },
      },
      {
        accessorKey: "createdAt",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Created" />
        ),
        cell: ({ row }) => formatDate(row.getValue("createdAt")),
      },
      {
        id: "actions",
        cell: ({ row }) => {
          // Don't show actions column if user has no permissions
          if (
            !permissions.canView &&
            !permissions.canEdit &&
            !permissions.canDelete
          ) {
            return null;
          }

          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  aria-label="Open menu"
                  variant="ghost"
                  className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                >
                  <MoreHorizontalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[160px]">
                {permissions.canView && (
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/quotes/${row.original.id}?title=${encodeURIComponent(row.original.name || "Unnamed Quote")}&mode=view`}
                    >
                      View
                    </Link>
                  </DropdownMenuItem>
                )}
                {permissions.canEdit && (
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/quotes/${row.original.id}?title=${encodeURIComponent(`Edit - ${row.original.name || "Unnamed Quote"}`)}&mode=edit`}
                    >
                      Edit
                    </Link>
                  </DropdownMenuItem>
                )}
                {permissions.canView && (
                  <DropdownMenuItem asChild>
                    <EnhancedQuoteGeneratorButton
                      quote={transformQuoteForPDF(row.original)}
                      templateId={templateId}
                      variant="download"
                      size="sm"
                      className="w-full justify-start p-0 h-auto font-normal"
                      options={{
                        company_name:
                          organizationData?.data?.organization?.name ||
                          "Flinkk CRM",
                        company_logo:
                          organizationData?.data?.organization?.logo ||
                          undefined,
                        company_address:
                          "Tech Park, Electronic City\nBangalore, Karnataka 560100\nIndia",
                      }}
                    >
                      <div className="flex items-center w-full px-2 py-1.5">
                        <FileTextIcon className="mr-2 h-4 w-4" />
                        Generate PDF {templateId && `(${templateName})`}
                      </div>
                    </EnhancedQuoteGeneratorButton>
                  </DropdownMenuItem>
                )}
                {(permissions.canEdit || permissions.canDelete) && (
                  <DropdownMenuSeparator />
                )}
                {permissions.canDelete && (
                  <DropdownMenuItem
                    onClick={() => deleteQuote(row.original.id)}
                    className="text-destructive"
                  >
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [deleteQuote],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    clearOnDefault: true,
  });

  return (
    <div className="space-y-4">
      <DataTable table={table} doctype="quote" onRowClick={handleRowClick}>
        <DataTableToolbar
          table={table}
          buttonText={undefined}
          href={undefined}
        />
      </DataTable>
    </div>
  );
}

// Helper function to format dates
const formatDate = (date: string | Date) => {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};
