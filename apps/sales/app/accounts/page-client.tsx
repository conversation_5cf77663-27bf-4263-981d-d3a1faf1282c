"use client";

import type { BusinessAccount } from "@/types/account";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDelete } from "@flinkk/hooks";
import { getAccountsTableColumns } from "./columns";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog,
    ),
  {
    ssr: false,
  },
);

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface AccountsTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

export function AccountsTable({
  data,
  pageCount,
  permissions,
}: AccountsTableProps) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<BusinessAccount> | null>(null);

  const { deleteRecord, isLoading: isDeleting } = useDelete({
    model: "businessAccount",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setRowAction(null);
      router.refresh();
    },
    successMessage: "Account deleted successfully",
    errorMessage: "Failed to delete account",
  });

  const handleDeleteAccount = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      await deleteRecord(rowAction.row.id);
    }
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    }
  }, [rowAction]);

  const columns = React.useMemo(
    () =>
      getAccountsTableColumns({
        setRowAction,
      }),
    [setRowAction],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Use account name for the tab title
          const accountName = row.name || "Unnamed Account";

          // Create URL with title parameter
          const url = `/accounts/${row.id}/view?title=${encodeURIComponent(accountName)}`;
          router.push(url);
        }}
        doctype="account"
      >
        <DataTableToolbar
          table={table}
          buttonText={permissions?.canCreate ? "New Account" : undefined}
          href={permissions?.canCreate ? "/accounts/new" : undefined}
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeleteAccount}
        />
      )}
    </>
  );
}
