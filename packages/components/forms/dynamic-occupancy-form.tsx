"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@flinkk/components/ui/card";
import { Loader2Icon } from "lucide-react";
import { NumberFieldFormElement } from "@flinkk/dynamic-form/form-elements";
import { useOccupancyConfig } from "@flinkk/hooks/query/use-occupancy-config";
import { OccupancyConfig } from "@flinkk/inventory-api";

// Type mapping for UI labels
const TYPE_LABELS: Record<string, string> = {
  EXTRA_ADULT: "Adults",
  CHILD: "Children",
  INFANT: "Infant",
  EXTRA_BED: "Extra Bed",
  COT: "Baby Cot",
  EXTRA_ADULT_BEYOND_CAPACITY: "Extra Adult (Overflow)",
  BASE_1: "Base", // Hidden/ignored if needed
};

interface OccupancyFormData {
  [key: string]: number;
}

interface OccupancyPayload {
  occupancy: Array<{
    occupancy_config_id: string;
    quantity: number;
  }>;
}

interface DynamicOccupancyFormProps {
  hotelId: string;
  onSubmit: (data: OccupancyPayload) => void;
  initialValues?: Record<string, number>;
  isSubmitting?: boolean;
  disabled?: boolean;
  title?: string;
  showCard?: boolean;
}

export function DynamicOccupancyForm({
  hotelId,
  onSubmit,
  initialValues = {},
  isSubmitting = false,
  disabled = false,
  title = "Occupancy Details",
  showCard = true,
}: DynamicOccupancyFormProps) {
  const [formSchema, setFormSchema] = useState<z.ZodSchema<any> | null>(null);

  // Fetch occupancy configs
  const {
    data: occupancyData,
    isLoading,
    error,
  } = useOccupancyConfig({
    hotelId,
    enabled: !!hotelId,
  });

  const occupancyConfigs: OccupancyConfig[] = (occupancyData as any)?.occupancy_configs || [];

  // Create dynamic schema based on occupancy configs
  useEffect(() => {
    if (occupancyConfigs.length > 0) {
      const schemaFields: Record<string, z.ZodNumber> = {};

      occupancyConfigs.forEach((config) => {
        let fieldSchema = z.number().min(0, `${config.name} cannot be negative`);

        // Add min/max validation based on config
        if (config.min_occupancy > 0) {
          fieldSchema = fieldSchema.min(
            config.min_occupancy,
            `${config.name} must be at least ${config.min_occupancy}`
          );
        }

        if (config.max_occupancy) {
          fieldSchema = fieldSchema.max(
            config.max_occupancy,
            `${config.name} cannot exceed ${config.max_occupancy}`
          );
        }

        // Make required fields non-zero if is_default is true
        if (config.is_default) {
          fieldSchema = fieldSchema.min(
            Math.max(1, config.min_occupancy),
            `${config.name} is required`
          );
        }

        schemaFields[config.id] = fieldSchema;
      });

      setFormSchema(z.object(schemaFields));
    }
  }, [occupancyConfigs]);

  // Initialize form with default values
  const form = useForm<OccupancyFormData>({
    resolver: formSchema ? zodResolver(formSchema) : undefined,
    defaultValues: occupancyConfigs.reduce((acc, config) => {
      acc[config.id] = initialValues[config.id] || 0;
      return acc;
    }, {} as OccupancyFormData),
  });

  // Reset form when configs or initial values change
  useEffect(() => {
    if (occupancyConfigs.length > 0) {
      const defaultValues = occupancyConfigs.reduce((acc, config) => {
        acc[config.id] = initialValues[config.id] || 0;
        return acc;
      }, {} as OccupancyFormData);
      
      form.reset(defaultValues);
    }
  }, [occupancyConfigs, initialValues, form]);

  const handleSubmit = (data: OccupancyFormData) => {
    // Build payload with only non-zero quantities
    const occupancy = occupancyConfigs
      .filter((config) => data[config.id] > 0)
      .map((config) => ({
        occupancy_config_id: config.id,
        quantity: data[config.id],
      }));

    onSubmit({ occupancy });
  };

  const LoadingContent = () => (
    <div className="flex items-center justify-center p-6">
      <Loader2Icon className="h-6 w-6 animate-spin" />
      <span className="ml-2">Loading occupancy configuration...</span>
    </div>
  );

  const ErrorContent = () => (
    <div className="p-6">
      <p className="text-destructive">
        Failed to load occupancy configuration. Please try again.
      </p>
    </div>
  );

  const EmptyContent = () => (
    <div className="p-6">
      <p className="text-muted-foreground">
        No occupancy configuration found for this hotel.
      </p>
    </div>
  );

  const FormContent = () => (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {occupancyConfigs
            .filter((config) => config.type !== "BASE_1") // Hide BASE_1 if needed
            .map((config) => (
              <NumberFieldFormElement
                key={config.id}
                control={form.control}
                name={config.id}
                label={`${TYPE_LABELS[config.type] || config.name}${
                  config.is_default ? " *" : ""
                }`}
                placeholder={`Enter ${TYPE_LABELS[config.type] || config.name.toLowerCase()}`}
                required={config.is_default}
                min={config.min_occupancy}
                max={config.max_occupancy}
                disabled={disabled}
                helperText={
                  config.min_occupancy > 0 || config.max_occupancy
                    ? `Range: ${config.min_occupancy} - ${config.max_occupancy}`
                    : undefined
                }
              />
            ))}
        </div>

        <div className="flex justify-end space-x-2">
          <Button
            type="submit"
            disabled={isSubmitting || disabled}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <>
                <Loader2Icon className="h-4 w-4 animate-spin mr-2" />
                Submitting...
              </>
            ) : (
              "Submit"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );

  if (isLoading) {
    return showCard ? (
      <Card>
        <CardContent>
          <LoadingContent />
        </CardContent>
      </Card>
    ) : (
      <LoadingContent />
    );
  }

  if (error) {
    return showCard ? (
      <Card>
        <CardContent>
          <ErrorContent />
        </CardContent>
      </Card>
    ) : (
      <ErrorContent />
    );
  }

  if (occupancyConfigs.length === 0) {
    return showCard ? (
      <Card>
        <CardContent>
          <EmptyContent />
        </CardContent>
      </Card>
    ) : (
      <EmptyContent />
    );
  }

  if (!formSchema) {
    return null;
  }

  if (showCard) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <FormContent />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {title && <h3 className="text-lg font-medium">{title}</h3>}
      <FormContent />
    </div>
  );
}
