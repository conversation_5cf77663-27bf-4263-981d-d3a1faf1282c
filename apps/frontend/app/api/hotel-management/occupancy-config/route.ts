import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";
import { prisma } from "@flinkk/database/prisma";

export const dynamic = "force-dynamic";

// GET /api/hotel-management/occupancy-config
export async function GET(request: NextRequest) {
  try {
    const { tenantId } = await getToken({ req: request });

    if (!tenantId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const hotelId = searchParams.get("hotel_id");

    if (!hotelId) {
      return NextResponse.json(
        { error: "hotel_id is required" },
        { status: 400 },
      );
    }

    // Get inventory configuration for the tenant
    const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
      where: {
        tenantId,
      },
      select: {
        apiUrl: true,
        token: true,
        isActive: true,
        verificationStatus: true,
      },
    });

    // Check if inventory is configured and active
    if (!inventoryConfig || !inventoryConfig.apiUrl || !inventoryConfig.token) {
      console.log("Inventory not configured");
      return NextResponse.json(
        { error: "Inventory service not configured" },
        { status: 503 },
      );
    }

    if (
      !inventoryConfig.isActive ||
      inventoryConfig.verificationStatus !== "connected"
    ) {
      console.log("Inventory service not available");
      return NextResponse.json(
        { error: "Inventory service not available" },
        { status: 503 },
      );
    }

    // Create inventory API instance
    const inventoryAPI = new FlinkkInventoryAPI({
      apiUrl: inventoryConfig.apiUrl,
      token: inventoryConfig.token,
    });

    try {
      // Make request to inventory API for hotel occupancy config
      const occupancyData = await inventoryAPI.getHotelOccupancyConfig({
        hotel_id: hotelId,
      });

      console.log("Successfully fetched occupancy config data from inventory API");
      return NextResponse.json(occupancyData);
    } catch (inventoryError: any) {
      console.error("Inventory API error:", inventoryError);
      return NextResponse.json(
        { error: "Failed to fetch occupancy config data from inventory service" },
        { status: 502 },
      );
    }
  } catch (error) {
    console.error("Error in occupancy config API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
