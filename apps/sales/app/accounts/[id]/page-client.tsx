"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import toast from "react-hot-toast";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { useFormCancellation } from "@flinkk/hooks/form";
import useFormPersist from "react-hook-form-persist";
import { Button } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { Loader2Icon } from "lucide-react";
import SectionLoading from "./sections/loading";

/* Dynamic sections */
const BasicInformation = dynamic(
  () => import("./sections/01-basic-information"),
  { loading: () => <SectionLoading /> }
);
const ContactManagement = dynamic(
  () => import("./sections/02-contact-management"),
  { loading: () => <SectionLoading /> }
);
const AddressInformation = dynamic(
  () => import("./sections/03-address-information"),
  { loading: () => <SectionLoading /> }
);
const AdditionalInformation = dynamic(
  () => import("./sections/04-additional-information"),
  { loading: () => <SectionLoading /> }
);
const AccountCustomFieldsSection = dynamic(
  () => import("./sections/05-custom-fields"),
  { loading: () => <SectionLoading /> }
);

import { FieldPermissionsData } from "@flinkk/shared-auth/utils/field-permissions-utils";

// Zod schema for account form validation
const accountFormSchema = z.object({
  name: z
    .string()
    .min(1, "Account name is required")
    .max(100, "Account name must be less than 100 characters"),
  industry: z
    .string()
    .max(100, "Industry must be less than 100 characters")
    .optional(),
  website: z
    .string()
    .url("Website must be a valid URL")
    .optional()
    .or(z.literal("")),
  phone: z
    .string()
    .max(20, "Phone number must be less than 20 characters")
    .optional(),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  employees: z
    .number()
    .min(0, "Number of employees must be a positive number")
    .max(1000000, "Number of employees is too large")
    .optional(),
  annualRevenue: z
    .string()
    .max(50, "Annual revenue must be less than 50 characters")
    .optional(),
  billingAddress: z
    .string()
    .max(500, "Billing address must be less than 500 characters")
    .optional(),
  shippingAddress: z
    .string()
    .max(500, "Shipping address must be less than 500 characters")
    .optional(),
  description: z
    .string()
    .max(2000, "Description must be less than 2000 characters")
    .optional(),
  type: z.enum(["CUSTOMER", "PROSPECT", "PARTNER", "VENDOR", "OTHER"]),
  userId: z.string().min(1, "Account owner is required"),
  primaryContactId: z.string().optional(),
  tags: z.string().max(500, "Tags must be less than 500 characters").optional(),
  region: z
    .string()
    .max(100, "Region must be less than 100 characters")
    .optional(),
  emailDomain: z
    .string()
    .max(100, "Email domain must be less than 100 characters")
    .optional(),
});

type AccountFormValues = z.infer<typeof accountFormSchema>;

interface NewAccountFormProps {
  id?: string;
  initialData?: any;
  fieldPermissionsData: FieldPermissionsData;
  customFields?: CustomField[];
}

// Removed User and Contact interfaces since we're using DynamicSelectFieldFormElement

// Define CustomFieldType enum locally
enum CustomFieldType {
  TEXT = "TEXT",
  TEXTAREA = "TEXTAREA",
  RICH_TEXT = "RICH_TEXT",
  NUMBER = "NUMBER",
  CURRENCY = "CURRENCY",
  PERCENT = "PERCENT",
  DATE = "DATE",
  DATETIME = "DATETIME",
  TIME = "TIME",
  BOOLEAN = "BOOLEAN",
  CHECKBOX = "CHECKBOX",
  SELECT = "SELECT",
  MULTI_SELECT = "MULTI_SELECT",
  PICKLIST = "PICKLIST",
  URL = "URL",
  EMAIL = "EMAIL",
  PHONE = "PHONE",
  FORMULA = "FORMULA",
  LOOKUP = "LOOKUP",
  GEOLOCATION = "GEOLOCATION",
  FILE = "FILE",
  IMAGE = "IMAGE",
}

interface CustomField {
  id: string;
  name: string;
  label: string;
  description?: string | null;
  type: CustomFieldType;
  isRequired: boolean;
  defaultValue?: string | null;
  placeholder?: string | null;
  helpText?: string | null;
  options?: any;
  value?: string | null;
}

export function NewAccountForm({
  id,
  initialData,
  fieldPermissionsData,
  customFields = [],
}: NewAccountFormProps) {
  const router = useRouter();
  const isEditMode = id !== "new" && initialData;

  // Storage key for form persistence
  const storageKey = `account-form-${id}`;
  const shouldPersist = id === "new";

  // Flag to prevent saving after successful submission
  const [isSubmissionSuccessful, setIsSubmissionSuccessful] = useState(false);

  // Use the new useSaveFormData hook
  const { save: saveAccount, isLoading: isSaving } = useSaveFormData({
    model: "businessAccount",
    clearFormPersistence: shouldPersist
      ? () => {
          if (typeof window !== "undefined") {
            setIsSubmissionSuccessful(true);
            sessionStorage.removeItem(storageKey);
            sessionStorage.removeItem("account-form-new");
          }
        }
      : undefined,
    onSuccess: () => {
      toast.success(
        `Account ${isEditMode ? "updated" : "created"} successfully`
      );
      router.push("/accounts");
    },
    onError: (error) => {
      toast.error(
        `Failed to ${isEditMode ? "update" : "create"} account. Please try again.`
      );
    },
  });

  // Initialize React Hook Form with Zod validation
  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      industry: initialData?.industry || "",
      website: initialData?.website || "",
      phone: initialData?.phone || "",
      email: initialData?.email || "",
      employees: initialData?.employees || 0,
      annualRevenue: initialData?.annualRevenue || "",
      billingAddress: initialData?.billingAddress || "",
      shippingAddress: initialData?.shippingAddress || "",
      description: initialData?.description || "",
      type: initialData?.type || "CUSTOMER",
      userId: initialData?.userId || "",
      primaryContactId: initialData?.primaryContactId || "",
      tags: initialData?.tags || "",
      region: initialData?.region || "",
      emailDomain: initialData?.emailDomain || "",
    },
  });

  // Set up form persistence for new accounts
  useFormPersist(storageKey, {
    watch: form.watch,
    setValue: form.setValue,
    storage:
      shouldPersist && typeof window !== "undefined" && !isSubmissionSuccessful
        ? window.sessionStorage
        : undefined,
  });

  // Handle form cancellation with navigation cleanup
  const { handleCancel } = useFormCancellation({
    recordId: id,
    redirectPath: "/accounts",
    clearFormPersistence: shouldPersist
      ? () => {
          if (typeof window !== "undefined") {
            sessionStorage.removeItem(storageKey);
          }
        }
      : undefined,
  });

  // Initialize custom fields state with server-provided data
  const [customFieldsState, setCustomFieldsState] =
    useState<CustomField[]>(customFields);
  const [customFieldValues, setCustomFieldValues] = useState<
    Record<string, any>
  >({});

  // Initialize custom field values from initial data
  useEffect(() => {
    if (initialData?.customFields) {
      const initialCustomFieldValues: Record<string, any> = {};
      initialData.customFields.forEach((field: any) => {
        if (field.value !== null) {
          initialCustomFieldValues[field.id] = field.value;
        }
      });
      setCustomFieldValues(initialCustomFieldValues);
    }
  }, [initialData]);

  // Handle custom field changes
  const handleCustomFieldChange = (fieldId: string, value: string | null) => {
    setCustomFieldValues((prev) => ({
      ...prev,
      [fieldId]: value,
    }));
  };

  // Handle form submission using the new hook
  const onSubmit = async (data: AccountFormValues) => {
    // Prepare the data for submission
    const submissionData = {
      ...data,
      customFields: customFieldValues,
    };

    // Fix: If primaryContactId is an empty string, remove it from the payload
    if (submissionData.primaryContactId === "") {
      delete submissionData.primaryContactId;
    }

    try {
      // Use the new save function from the hook
      await saveAccount(id, submissionData);

      // Additional cleanup after successful submission
      if (shouldPersist && typeof window !== "undefined") {
        setIsSubmissionSuccessful(true);
        sessionStorage.removeItem(storageKey);
        sessionStorage.removeItem("account-form-new");
      }
    } catch (error) {
      // Don't clear storage if submission failed
      throw error;
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Sections (dynamically loaded) */}
        <BasicInformation control={form.control} />

        <ContactManagement control={form.control} />

        <AddressInformation control={form.control} />

        <AdditionalInformation control={form.control} />

        {/* Custom Fields Section (dynamically loaded; placeholder) */}
        {customFields.length > 0 && <AccountCustomFieldsSection />}

        {/* Submit Button */}
        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
            {isEditMode ? "Update Account" : "Create Account"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
