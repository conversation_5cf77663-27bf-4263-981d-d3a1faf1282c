import { getActivityByEntity } from "@flinkk/shared-slots-activity/page-actions";
import { PageClient } from "@flinkk/shared-slots-activity/page-client";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import {
  getOpportunityById,
  isInventoryConfigurationVerified,
} from "../actions";
import { InfoCardWrapper } from "../_components/info-card-wrapper";
import { getStatusOptions } from "@/lib/actions/status-options";
import { getQuotesByOpportunity } from "../@quotes/actions";
import { QuotesSection } from "../_components/quotes-section";
import { ProductsSection } from "../_components/products-section";
import { PackagesSectionWrapper } from "../_components/packages-section-wrapper";
import {
  groupCustomFields,
  sortGroupsByPriority,
  transformFieldsForInfoCard,
} from "../_utils/custom-fields-grouping";
import type { ActivityLog } from "@flinkk/shared-slots-activity/types/activity";

/**
 * Enhanced activity fetching that includes activities from the associated lead
 * if the opportunity was converted from a lead
 */
async function getEnhancedActivityLogs(
  opportunityId: string,
  tenantId: string,
  convertedFromLeadId?: string | null,
): Promise<ActivityLog[]> {
  try {
    // Always fetch opportunity activities
    const opportunityActivities = await getActivityByEntity(
      "OPPORTUNITY",
      opportunityId,
      tenantId,
    );

    // If no associated lead, return only opportunity activities
    if (!convertedFromLeadId) {
      return opportunityActivities;
    }

    // Fetch lead activities if there's an associated lead
    const leadActivities = await getActivityByEntity(
      "LEAD",
      convertedFromLeadId,
      tenantId,
    );

    // Combine and sort all activities by date (most recent first)
    const allActivities = [...opportunityActivities, ...leadActivities];

    return allActivities.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
    );
  } catch (error) {
    console.error("Error fetching enhanced activity logs:", error);
    // Fallback to opportunity activities only
    return getActivityByEntity("OPPORTUNITY", opportunityId, tenantId);
  }
}

export default async function OpportunityActivityPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;

  const { tenantId, userId } = await getServerSession();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const opportunityId = params.id;
  const [opportunity, , quotesData, isInventoryVerified] = await Promise.all([
    getOpportunityById(opportunityId),
    getStatusOptions("opportunity"),
    getQuotesByOpportunity(opportunityId),
    isInventoryConfigurationVerified(),
  ]);

  // Fetch enhanced activity logs that include lead activities if applicable
  const activityLogs = await getEnhancedActivityLogs(
    opportunityId,
    tenantId,
    opportunity.convertedFromLeadId,
  );

  // Process custom fields if they exist
  const customFields = opportunity.customFields || [];
  const groupedCustomFields = groupCustomFields(customFields);
  const sortedGroups = sortGroupsByPriority(groupedCustomFields);

  return (
    <div className="space-y-4">
      {/* Custom Fields Cards - Now at the top of the right panel */}
      {sortedGroups.map(([groupName, fields]) => (
        <InfoCardWrapper
          key={groupName}
          title={groupName}
          opportunityId={opportunityId}
          formFields={transformFieldsForInfoCard(fields, opportunityId) as any}
        />
      ))}

      {/* Assignment & Status Card */}
      <InfoCardWrapper
        title="Assignment & Status"
        opportunityId={opportunityId}
        formFields={[
          {
            name: "status",
            label: "Status",
            type: "dynamic-select",
            value: opportunity.status,
            displayValue: opportunity.status
              ? opportunity.status.replace(/_/g, " ")
              : null,
            required: true,
            dynamicKey: "opportunity-status",
            placeholder: "Select status",
          },
          {
            name: "userId",
            label: "Assigned To",
            type: "dynamic-select",
            value: opportunity.userId,
            displayValue:
              opportunity?.user?.name ||
              opportunity?.user?.email ||
              "Unassigned",
            showEmpty: true,
            dynamicKey: "tenant-members",
            placeholder: "Select team member",
          },
        ]}
      />

      {/* Opportunity Qualification Card */}
      <InfoCardWrapper
        title="Opportunity Qualification"
        opportunityId={opportunityId}
        formFields={[
          {
            name: "priority",
            label: "Priority",
            type: "select",
            value: opportunity.priority,
            displayValue: opportunity.priority,
            showEmpty: true,
            options: [
              { value: "LOW", label: "Low" },
              { value: "MEDIUM", label: "Medium" },
              { value: "HIGH", label: "High" },
            ],
          },
          {
            name: "relatedToType",
            label: "Related To",
            type: "select",
            value: opportunity.relatedToType,
            displayValue: opportunity.relatedToType,
            showEmpty: true,
            options: [
              { value: "LEAD", label: "Lead" },
              { value: "CONTACT", label: "Contact" },
              { value: "ACCOUNT", label: "Account" },
            ],
          },
          {
            name: "type",
            label: "Opportunity Type",
            type: "select",
            value: opportunity.type,
            displayValue: opportunity.type,
            showEmpty: true,
            options: [
              { value: "NEW_BUSINESS", label: "New Business" },
              { value: "EXISTING_BUSINESS", label: "Existing Business" },
              { value: "RENEWAL", label: "Renewal" },
              { value: "UPSELL", label: "Upsell" },
            ],
          },
        ]}
      />

      {/* Quotes Section */}
      <QuotesSection
        opportunityId={opportunityId}
        quotesData={quotesData.data}
        opportunity={opportunity}
        opportunityStage={opportunity.stage}
      />

      {/* Products Section - Only show if inventory configuration is not verified */}
      {!isInventoryVerified && (
        <ProductsSection
          opportunityId={opportunityId}
          opportunityStage={opportunity.stage}
        />
      )}

      {/* Packages Section - Only show if inventory configuration is not verified */}
      {!isInventoryVerified && (
        <PackagesSectionWrapper
          opportunityId={opportunityId}
          opportunityStage={opportunity.stage}
        />
      )}

      {/* Activity Log Section - Wrapped in error boundary */}
      <div className="activity-section-wrapper">
        <InfoCardWrapper title="Activity Log">
          <PageClient
            entityType="Opportunity"
            entityId={opportunityId}
            activityLogs={activityLogs}
            userId={userId}
          />
        </InfoCardWrapper>
      </div>
    </div>
  );
}
