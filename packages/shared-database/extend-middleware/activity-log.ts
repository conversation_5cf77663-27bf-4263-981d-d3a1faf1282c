import { Prisma } from "@prisma/client";
import { getContext } from "../context";

/**
 * Configuration options for activity logging middleware
 */
export interface ActivityLogOptions {
  /** Whether to require context (userId, tenantId) from async local storage */
  requireContext?: boolean;
  /** Default tenant ID to use when context is not available */
  defaultTenantId?: string;
  /** Default user ID to use when context is not available (can be null for system operations) */
  defaultUserId?: string | null;
  /** Whether to create logs for system operations (without user) */
  allowSystemLogs?: boolean;
  /** Custom log prefix for system operations */
  systemLogPrefix?: string;
  /** Whether to skip logging when context is missing (instead of using defaults) */
  skipOnMissingContext?: boolean;
}

/**
 * Extends Prisma Client with extension for activity logging
 * This extension automatically creates activity logs for specific models and actions
 * Supports both context-dependent and context-free logging
 *
 * @param options Configuration options for activity logging
 * @returns The Prisma Client extension
 */
export function extendActivityLogMiddleware(options: ActivityLogOptions = {}) {
  const {
    requireContext = true,
    defaultTenantId,
    defaultUserId = null,
    allowSystemLogs = true,
    systemLogPrefix = "System",
    skipOnMissingContext = false,
  } = options;
  return Prisma.defineExtension({
    name: "activityLog",
    query: {
      $allModels: {
        async $allOperations({ model, operation, args, query }) {
          // Execute the query first
          const result = await query(args);

          // Only proceed with activity logging for specific models and actions
          const loggableModels = [
            "Lead",
            "Contact",
            "BusinessAccount",
            "Opportunity",
            "Task",
            "SupportTicket",
          ];

          if (
            loggableModels.includes(model) &&
            ["create", "update", "delete"].includes(operation)
          ) {
            try {
              // Get context (userId, tenantId) from the async local storage
              const context = getContext();

              let userId: string | undefined | null;
              let tenantId: string | undefined;

              if (requireContext) {
                // Original behavior: require context
                if (!context || !context.tenantId) {
                  console.warn(
                    `Activity logging skipped: Missing context for ${model} ${operation}`,
                  );
                  return result;
                }
                userId = context.userId;
                tenantId = context.tenantId;
              } else {
                // Context-free behavior: use context if available, otherwise use defaults
                if (context && context.tenantId) {
                  userId = context.userId;
                  tenantId = context.tenantId;
                } else {
                  // Use defaults or skip based on configuration
                  if (skipOnMissingContext && !defaultTenantId) {
                    console.warn(
                      `Activity logging skipped: No context and no defaults for ${model} ${operation}`,
                    );
                    return result;
                  }

                  if (!defaultTenantId && !allowSystemLogs) {
                    console.warn(
                      `Activity logging skipped: No tenant ID and system logs not allowed for ${model} ${operation}`,
                    );
                    return result;
                  }

                  userId = defaultUserId;
                  tenantId = defaultTenantId;
                }
              }

              // Prepare activity log data
              let title = " ";
              let description = "";
              // Map model names to RelatedToType enum values
              let related_to_type = model.toUpperCase(); // Default conversion
              if (model === "Opportunity") {
                related_to_type = "OPPORTUNITY"; // Map Opportunity to OPPORTUNITY in the enum
              } else if (model === "BusinessAccount") {
                related_to_type = "ACCOUNT"; // Map BusinessAccount to ACCOUNT in the enum
              } else if (model === "SupportTicket") {
                related_to_type = "TICKET"; // Map SupportTicket to TICKET in the enum
              }
              let related_to_id = "";
              let action = "";

              // Type assertion for result
              const entityResult = result as any;

              // Set related_to_id and action based on the operation
              if (operation === "create") {
                related_to_id = entityResult?.id || "";
                action = "CREATE";
              } else if (operation === "update") {
                related_to_id = entityResult?.id || "";
                action = "UPDATE";
              } else if (operation === "delete" && args.where?.id) {
                related_to_id = args.where.id;
                action = "DELETE";
              }

              // Generate title and description based on model and operation
              const isSystemOperation = !userId;
              const prefix = isSystemOperation ? systemLogPrefix : "";

              if (operation === "create") {
                title = `${prefix ? prefix + " - " : ""}${model} Created`;
                description = `A new ${model.toLowerCase()} was created${isSystemOperation ? " by system" : ""}`;

                // Add more specific details if available
                if (model === "Lead") {
                  // For leads, construct name from firstName and lastName
                  const leadName =
                    [entityResult?.firstName, entityResult?.lastName]
                      .filter(Boolean)
                      .join(" ") || "Unnamed Lead";
                  description = `Lead "${leadName}" was created${isSystemOperation ? " by system" : ""}`;
                } else if (model === "Opportunity") {
                  // For opportunities, use dealName field
                  const dealName =
                    entityResult?.dealName ||
                    entityResult?.name ||
                    "Unnamed Opportunity";
                  description = `Opportunity "${dealName}" was created${isSystemOperation ? " by system" : ""}`;
                } else if (model === "SupportTicket") {
                  // For support tickets, use subject field
                  const ticketSubject =
                    entityResult?.subject || "Untitled Ticket";
                  description = `Support Ticket "${ticketSubject}" was created${isSystemOperation ? " by system" : ""}`;
                } else if (entityResult?.name) {
                  description = `${model} "${entityResult.name}" was created${isSystemOperation ? " by system" : ""}`;
                } else if (entityResult?.title) {
                  description = `${model} "${entityResult.title}" was created${isSystemOperation ? " by system" : ""}`;
                }
              } else if (operation === "update") {
                title = `${prefix ? prefix + " - " : ""}${model} Updated`;
                description = `${model} was updated${isSystemOperation ? " by system" : ""}`;

                // Add more specific details if available
                if (model === "Lead") {
                  // For leads, construct name from firstName and lastName
                  const leadName =
                    [entityResult?.firstName, entityResult?.lastName]
                      .filter(Boolean)
                      .join(" ") || "Unnamed Lead";
                  description = `Lead "${leadName}" was updated${isSystemOperation ? " by system" : ""}`;
                } else if (model === "Opportunity") {
                  // For opportunities, use dealName field
                  const dealName =
                    entityResult?.dealName ||
                    entityResult?.name ||
                    "Unnamed Opportunity";
                  description = `Opportunity "${dealName}" was updated${isSystemOperation ? " by system" : ""}`;
                } else if (model === "SupportTicket") {
                  // For support tickets, use subject field
                  const ticketSubject =
                    entityResult?.subject || "Untitled Ticket";
                  description = `Support Ticket "${ticketSubject}" was updated${isSystemOperation ? " by system" : ""}`;
                } else if (entityResult?.name) {
                  description = `${model} "${entityResult.name}" was updated${isSystemOperation ? " by system" : ""}`;
                } else if (entityResult?.title) {
                  description = `${model} "${entityResult.title}" was updated${isSystemOperation ? " by system" : ""}`;
                }

                // For status changes, make the title more specific
                const updateData = args.data as any;
                if (updateData?.status) {
                  title = `${prefix ? prefix + " - " : ""}Status changed to ${updateData.status}`;
                }
              } else if (operation === "delete") {
                title = `${prefix ? prefix + " - " : ""}${model} Deleted`;
                description = `${model} was deleted${isSystemOperation ? " by system" : ""}`;
              }

              // Create the activity log using the current query context
              // Note: We need to use a separate prisma instance to avoid infinite recursion
              const { PrismaClient } = await import("@prisma/client");
              const logPrisma = new PrismaClient();

              try {
                await (logPrisma.activityLog as any).create({
                  data: {
                    title,
                    description,
                    activity_type: isSystemOperation ? "SYSTEM" : "OTHER",
                    action: action,
                    related_to_type: related_to_type,
                    related_to_id: related_to_id,
                    user_id: userId || undefined,
                    tenantId: tenantId,
                    visibility: "INTERNAL", // Default visibility
                  },
                });
              } finally {
                await logPrisma.$disconnect();
              }
            } catch (error) {
              // Log the error but don't block the original operation
              console.error(
                `Error creating activity log for ${model} ${operation}:`,
                error,
              );
            }
          }

          // For Note creation, create an activity log for the parent entity
          if (model === "Note" && operation === "create" && result) {
            try {
              const context = getContext();

              if (!context || !context.userId || !context.tenantId) {
                console.warn(
                  `Activity logging skipped: Missing context for Note creation`,
                );
                return result;
              }

              const { userId, tenantId } = context;

              // Type assertion for Note result
              const noteResult = result as any;

              // Create an activity log for the parent entity (Lead, Task, etc.) using a separate prisma instance
              const { PrismaClient } = await import("@prisma/client");
              const logPrisma = new PrismaClient();

              try {
                // Determine the title and description based on the parent entity type
                let title = "Note Added";
                let description = "";

                if (noteResult.reference_modal === "Lead") {
                  title = "Note Added to Lead";
                  description = `${noteResult.title || "Untitled"}`;
                } else if (noteResult.reference_modal === "Task") {
                  title = "Note Added to Task";
                  description = `${noteResult.title || "Untitled"}`;
                } else if (noteResult.reference_modal === "Opportunity") {
                  title = "Note Added to Opportunity";
                  description = `${noteResult.title || "Untitled"}`;
                } else if (noteResult.reference_modal === "Contact") {
                  title = "Note Added to Contact";
                  description = `${noteResult.title || "Untitled"}`;
                } else if (noteResult.reference_modal === "Account") {
                  title = "Note Added to Account";
                  description = `${noteResult.title || "Untitled"}`;
                } else if (noteResult.reference_modal === "Ticket") {
                  title = "Note Added to Ticket";
                  description = `${noteResult.title || "Untitled"}`;
                } else {
                  title = `Note Added to ${noteResult.reference_modal}`;
                  description = `${noteResult.title || "Untitled"}`;
                }

                // Truncate content for description if it's too long
                const contentPreview =
                  noteResult.content && noteResult.content.length > 100
                    ? noteResult.content.substring(0, 100) + "..."
                    : noteResult.content || "";

                if (contentPreview) {
                  description += `: ${contentPreview}`;
                }

                // Map reference_modal to correct RelatedToType enum values
                let relatedToType = noteResult.reference_modal.toUpperCase();
                if (noteResult.reference_modal === "Opportunity") {
                  relatedToType = "OPPORTUNITY";
                } else if (
                  noteResult.reference_modal === "BusinessAccount" ||
                  noteResult.reference_modal === "Account"
                ) {
                  relatedToType = "ACCOUNT";
                } else if (noteResult.reference_modal === "Ticket") {
                  relatedToType = "TICKET";
                }

                await (logPrisma.activityLog as any).create({
                  data: {
                    title,
                    description,
                    activity_type: "NOTE",
                    activity_type_id: noteResult.id, // Store the note ID
                    action: "CREATE",
                    related_to_type: relatedToType, // Use mapped entity type
                    related_to_id: noteResult.reference_modalId, // Use parent entity ID
                    user_id: userId,
                    tenantId: tenantId,
                    visibility: "INTERNAL", // Default visibility for notes
                  },
                });
              } finally {
                await logPrisma.$disconnect();
              }
            } catch (error) {
              console.error("Error creating activity log for Note:", error);
            }
          }

          // For Task creation linked to a parent entity, create an activity log for the parent
          if (model === "Task" && operation === "create" && result) {
            try {
              // Type assertion for Task result
              const taskResult = result as any;

              // Only proceed if this task is linked to a supported parent entity
              if (
                ![
                  "Lead",
                  "Opportunity",
                  "Contact",
                  "Account",
                  "Ticket",
                ].includes(taskResult.reference_modal)
              ) {
                return result;
              }

              const context = getContext();

              if (!context || !context.userId || !context.tenantId) {
                console.warn(
                  `Activity logging skipped: Missing context for Task creation`,
                );
                return result;
              }

              const { userId, tenantId } = context;

              // Create an activity log for the parent lead using a separate prisma instance
              const { PrismaClient } = await import("@prisma/client");
              const logPrisma = new PrismaClient();

              try {
                // Create title and description for task creation based on parent entity type
                let title = `Task Added to ${taskResult.reference_modal}`;
                let description = `${taskResult.title || "Untitled Task"}`;

                // Add task description preview if available
                if (taskResult.description) {
                  const descriptionPreview =
                    taskResult.description.length > 100
                      ? taskResult.description.substring(0, 100) + "..."
                      : taskResult.description;
                  description += `: ${descriptionPreview}`;
                }

                // Map reference_modal to correct RelatedToType enum values
                let relatedToType = taskResult.reference_modal.toUpperCase();
                if (taskResult.reference_modal === "Opportunity") {
                  relatedToType = "OPPORTUNITY";
                } else if (
                  taskResult.reference_modal === "BusinessAccount" ||
                  taskResult.reference_modal === "Account"
                ) {
                  relatedToType = "ACCOUNT";
                } else if (taskResult.reference_modal === "Ticket") {
                  relatedToType = "TICKET";
                }

                await (logPrisma.activityLog as any).create({
                  data: {
                    title,
                    description,
                    activity_type: "TASK", // Task creation activity
                    activity_type_id: taskResult.id, // Store the task ID
                    action: "CREATE",
                    related_to_type: relatedToType, // Use mapped parent entity type
                    related_to_id: taskResult.reference_modalId, // Use parent entity ID
                    user_id: userId,
                    tenantId: tenantId,
                    visibility: "INTERNAL", // Default visibility
                  },
                });
              } finally {
                await logPrisma.$disconnect();
              }
            } catch (error) {
              console.error(
                "Error creating activity log for Task linked to parent entity:",
                error,
              );
            }
          }

          return result;
        },
      },
    },
  });
}

/**
 * Creates activity logs without requiring context (context-free)
 * Useful for API routes, background jobs, system operations, etc.
 */
export function extendActivityLogContextFree(options?: {
  defaultTenantId?: string;
  defaultUserId?: string | null;
  allowSystemLogs?: boolean;
  systemLogPrefix?: string;
}) {
  return extendActivityLogMiddleware({
    requireContext: false,
    skipOnMissingContext: false,
    ...options,
  });
}

/**
 * Creates activity logs for system operations only (no user context)
 * Useful for background processes, automated tasks, etc.
 */
export function extendSystemActivityLog(defaultTenantId?: string) {
  return extendActivityLogMiddleware({
    requireContext: false,
    defaultTenantId,
    defaultUserId: null,
    allowSystemLogs: true,
    systemLogPrefix: "System",
    skipOnMissingContext: false,
  });
}

/**
 * Manual activity log creation utility (without middleware)
 * Use this when you want to create activity logs manually in your code
 */
export async function createActivityLog(
  data: {
    title: string;
    description: string;
    action: "CREATE" | "UPDATE" | "DELETE" | "VIEW" | "CUSTOM";
    related_to_type: string;
    related_to_id: string;
    activity_type?: string;
    activity_type_id?: string;
    visibility?: "PUBLIC" | "INTERNAL" | "PRIVATE";
    tenantId?: string;
    userId?: string | null;
  },
  prismaInstance?: any,
): Promise<any> {
  const prisma =
    prismaInstance || (await import("@prisma/client")).PrismaClient;
  const shouldDisconnect = !prismaInstance;

  const logPrisma = typeof prisma === "function" ? new prisma() : prisma;

  try {
    const activityLog = await logPrisma.activityLog.create({
      data: {
        title: data.title,
        description: data.description,
        action: data.action,
        related_to_type: data.related_to_type,
        related_to_id: data.related_to_id,
        activity_type: data.activity_type || "OTHER",
        activity_type_id: data.activity_type_id,
        visibility: data.visibility || "INTERNAL",
        user_id: data.userId || undefined,
        tenantId: data.tenantId || undefined,
      },
      include: {
        user: data.userId
          ? {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            }
          : false,
      },
    });

    return activityLog;
  } catch (error) {
    console.error("Error creating manual activity log:", error);
    throw error;
  } finally {
    if (shouldDisconnect && typeof logPrisma.$disconnect === "function") {
      await logPrisma.$disconnect();
    }
  }
}
