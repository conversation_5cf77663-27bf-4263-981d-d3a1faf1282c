"use server";

export type Opportunity = {
  id: string;
  name: string;
  stage: string;
  value: number;
  currency: string;
  status: any;
  updatedAt: string;
};

const formatCurrency = (cents: number, currency: string) =>
  new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency,
    maximumFractionDigits: 0,
  }).format(cents / 100);

/**
 * Fetch opportunities scoped to a contact id.
 * Replace with real data-source integration (e.g., packages/services).
 * Must be safe: return [] on predictable errors.
 */
import { prisma } from "@flinkk/database/prisma";
import { getServerSession } from "@flinkk/shared-auth/server-session";

export async function getOpportunitiesByContactId(contactId: string): Promise<
  (Opportunity & {
    valueFormatted: string;
  })[]
> {
  if (!contactId) return [];

  try {
    const { tenantId } = await getServerSession();

    const rows = await prisma.opportunity.findMany({
      where: { contactId, tenantId },
      select: {
        id: true,
        name: true,
        stage: true,
        value: true,
        currency: true,
        status: true,
        updatedAt: true,
      },
      orderBy: { updatedAt: "desc" },
    });

    console.log({ rows });

    type Row = {
      id: string;
      name: string;
      stage: string | null;
      value: number | null;
      currency: string | null;
      status: string | null;
      updatedAt: Date;
    };

    return (rows as Row[]).map((r) => ({
      id: r.id,
      name: r.name,
      stage: r.stage ?? "Prospecting",
      value: r.value ?? 0,
      currency: r.currency ?? "INR",
      status: (r.status as Opportunity["status"]) ?? "open",
      updatedAt: r.updatedAt.toISOString(),
      valueFormatted: formatCurrency(r.value ?? 0, r.currency ?? "INR"),
    }));
  } catch (e) {
    // Fail-soft to isolate slot errors
    throw new Error("Failed to load opportunities");
  }
}
