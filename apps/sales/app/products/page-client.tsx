"use client";

import type { Product } from "@/types/product";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDelete } from "@flinkk/hooks";
import { getProductsTableColumns } from "./columns";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog,
    ),
  {
    ssr: false,
  },
);

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface ProductsTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

export function ProductsTable({
  data,
  pageCount,
  permissions,
}: ProductsTableProps) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Product> | null>(null);

  const { deleteRecord, isLoading: isDeleting } = useDelete({
    model: "product",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setRowAction(null);
      router.refresh();
    },
    successMessage: "Product deleted successfully",
    errorMessage: "Failed to delete product",
  });

  const handleDeleteProduct = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      await deleteRecord(rowAction.row.id);
    }
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    }
  }, [rowAction]);

  const columns = React.useMemo(
    () =>
      getProductsTableColumns({
        setRowAction,
        permissions,
      }),
    [setRowAction, permissions],
  );

  // Add a key based on data length to force re-render when data changes
  const dataKey = React.useMemo(
    () => `products-${data?.length || 0}-${Date.now()}`,
    [data],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
    key: dataKey, // Add a key to force re-render
  });

  return (
    <>
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Get product name for the tab title
          const productName = row.name || "Unnamed Product";

          // Create URL with title parameter
          const url = `/products/${row.id}/view?title=${encodeURIComponent(productName)}`;
          router.push(url);
        }}
        doctype="product"
      >
        <DataTableToolbar
          table={table}
          buttonText={permissions.canCreate ? "New Product" : undefined}
          href={permissions.canCreate ? "/products/new" : undefined}
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && permissions.canDelete && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeleteProduct}
        />
      )}
    </>
  );
}
