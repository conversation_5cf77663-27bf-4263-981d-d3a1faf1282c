import { prisma } from "@flinkk/database/prisma";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { MODEL_MAPPING } from "./rbac-config";
import { UserPermissionMapping } from "./types";

/**
 * Check if the current user has a specific permission
 */
export async function hasPermission(
  model: string,
  action: "create" | "read" | "update" | "delete",
): Promise<boolean> {
  try {
    const session = await getServerSession();
    if (!session?.userId || !session?.tenantId) {
      return false;
    }

    const permissions = await getUserPermissions(
      session.userId,
      session.tenantId,
    );
    return checkPermission(permissions, model, action);
  } catch (error) {
    console.error("Error checking permission:", error);
    return false;
  }
}

/**
 * Check if a user has a specific permission (for use in server components)
 */
export async function hasPermissionForUser(
  userId: string,
  tenantId: string,
  model: string,
  action: "create" | "read" | "update" | "delete",
): Promise<boolean> {
  try {
    const permissions = await getUserPermissions(userId, tenantId);
    return checkPermission(permissions, model, action);
  } catch (error) {
    console.error("Error checking permission for user:", error);
    return false;
  }
}

/**
 * Get all permissions for the current user
 */
export async function getCurrentUserPermissions(): Promise<
  UserPermissionMapping[]
> {
  try {
    const session = await getServerSession();
    if (!session?.userId || !session?.tenantId) {
      return [];
    }

    return await getUserPermissions(session.userId, session.tenantId);
  } catch (error) {
    console.error("Error getting current user permissions:", error);
    return [];
  }
}

/**
 * Check if the current user has any of the specified roles
 */
export async function hasRole(...roles: string[]): Promise<boolean> {
  try {
    const session = await getServerSession();
    if (!session?.userId || !session?.tenantId) {
      return false;
    }

    const membership = await prisma.memberShip.findFirst({
      where: {
        userId: session.userId,
        tenantId: session.tenantId,
      },
      include: {
        customRole: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!membership?.customRole) {
      return false;
    }

    return roles.includes(membership.customRole.name);
  } catch (error) {
    console.error("Error checking user role:", error);
    return false;
  }
}

/**
 * Check if the current user is an admin (Owner or System Manager)
 */
export async function isAdmin(): Promise<boolean> {
  return await hasRole("OWNER", "SYSTEM_MANAGER");
}

/**
 * Check if the current user can manage users
 */
export async function canManageUsers(): Promise<boolean> {
  return (
    (await hasPermission("user", "create")) &&
    (await hasPermission("user", "update"))
  );
}

/**
 * Check if the current user can manage roles
 */
export async function canManageRoles(): Promise<boolean> {
  return (
    (await hasPermission("customRole", "create")) &&
    (await hasPermission("customRole", "update"))
  );
}

/**
 * Get user permissions from database
 */
async function getUserPermissions(
  userId: string,
  tenantId: string,
): Promise<UserPermissionMapping[]> {
  try {
    const membership = await prisma.memberShip.findFirst({
      where: {
        userId,
        tenantId,
      },
      include: {
        customRole: {
          select: {
            name: true,
            permissions: {
              select: {
                app: true,
                module: true,
                canView: true,
                canCreate: true,
                canEdit: true,
                canDelete: true,
              },
            },
          },
        },
      },
    });
    console.log({ membership });

    if (!membership?.customRole) {
      return [];
    }

    return membership.customRole.permissions;
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return [];
  }
}

/**
 * Check if permissions allow a specific action on a model
 */
function checkPermission(
  permissions: UserPermissionMapping[],
  model: string,
  action: "create" | "read" | "update" | "delete",
): boolean {
  // Find the model mapping
  const modelMapping = Object.entries(MODEL_MAPPING).find(
    ([key, mapping]) =>
      mapping.prismaModel === model ||
      key === model ||
      mapping.synonyms?.includes(model),
  );

  if (!modelMapping) {
    console.warn(`Model mapping not found for: ${model}`);
    return false;
  }

  const [moduleKey] = modelMapping;

  // Find the permission for this module
  const permission = permissions.find((p) => p.module === moduleKey);

  if (!permission) {
    return false;
  }

  // Map action to permission field
  switch (action) {
    case "create":
      return permission.canCreate;
    case "read":
      return permission.canView;
    case "update":
      return permission.canEdit;
    case "delete":
      return permission.canDelete;
    default:
      return false;
  }
}

/**
 * Get all models that the current user can access
 */
export async function getAccessibleModels(): Promise<string[]> {
  try {
    const permissions = await getCurrentUserPermissions();
    const accessibleModels: string[] = [];

    permissions.forEach((permission) => {
      if (permission.canView) {
        // Find the corresponding Prisma model
        const modelMapping = Object.entries(MODEL_MAPPING).find(
          ([key]) => key === permission.module,
        );

        if (modelMapping) {
          accessibleModels.push(modelMapping[1].prismaModel);
        }
      }
    });

    return [...new Set(accessibleModels)]; // Remove duplicates
  } catch (error) {
    console.error("Error getting accessible models:", error);
    return [];
  }
}

/**
 * Validate if a user can perform an action before executing it
 */
export async function validateAction(
  model: string,
  action: "create" | "read" | "update" | "delete",
  errorMessage?: string,
): Promise<void> {
  const hasAccess = await hasPermission(model, action);

  if (!hasAccess) {
    throw new Error(
      errorMessage || `You don't have permission to ${action} ${model}.`,
    );
  }
}

/**
 * Create a permission checker function for a specific user
 */
export function createPermissionChecker(userId: string, tenantId: string) {
  return {
    hasPermission: (
      model: string,
      action: "create" | "read" | "update" | "delete",
    ) => hasPermissionForUser(userId, tenantId, model, action),

    validateAction: async (
      model: string,
      action: "create" | "read" | "update" | "delete",
      errorMessage?: string,
    ) => {
      const hasAccess = await hasPermissionForUser(
        userId,
        tenantId,
        model,
        action,
      );
      if (!hasAccess) {
        throw new Error(
          errorMessage || `You don't have permission to ${action} ${model}.`,
        );
      }
    },
  };
}

/**
 * Batch permission checker - check multiple permissions at once
 */
export async function hasMultiplePermissions(
  permissions: Array<{
    model: string;
    action: "create" | "read" | "update" | "delete";
  }>,
): Promise<Record<string, boolean>> {
  const results: Record<string, boolean> = {};

  for (const { model, action } of permissions) {
    const key = `${model}:${action}`;
    results[key] = await hasPermission(model, action);
  }

  return results;
}

/**
 * Check if user has all specified permissions
 */
export async function hasAllPermissions(
  permissions: Array<{
    model: string;
    action: "create" | "read" | "update" | "delete";
  }>,
): Promise<boolean> {
  const results = await hasMultiplePermissions(permissions);
  return Object.values(results).every((hasAccess) => hasAccess);
}

/**
 * Check if user has any of the specified permissions
 */
export async function hasAnyPermission(
  permissions: Array<{
    model: string;
    action: "create" | "read" | "update" | "delete";
  }>,
): Promise<boolean> {
  const results = await hasMultiplePermissions(permissions);
  return Object.values(results).some((hasAccess) => hasAccess);
}

/**
 * Get all permissions for a specific model (canView, canCreate, canEdit, canDelete)
 * This is the centralized function to replace individual validateAction() calls
 */
export async function getModelPermissions(model: string): Promise<{
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}> {
  try {
    const session = await getServerSession();
    if (!session?.userId || !session?.tenantId) {
      return {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      };
    }

    const permissions = await getUserPermissions(
      session.userId,
      session.tenantId,
    );

    return {
      canView: checkPermission(permissions, model, "read"),
      canCreate: checkPermission(permissions, model, "create"),
      canEdit: checkPermission(permissions, model, "update"),
      canDelete: checkPermission(permissions, model, "delete"),
    };
  } catch (error) {
    console.error("Error getting model permissions:", error);
    return {
      canView: false,
      canCreate: false,
      canEdit: false,
      canDelete: false,
    };
  }
}

/**
 * Get permissions for multiple models at once
 */
export async function getMultipleModelPermissions(models: string[]): Promise<
  Record<
    string,
    {
      canView: boolean;
      canCreate: boolean;
      canEdit: boolean;
      canDelete: boolean;
    }
  >
> {
  const results: Record<
    string,
    {
      canView: boolean;
      canCreate: boolean;
      canEdit: boolean;
      canDelete: boolean;
    }
  > = {};

  for (const model of models) {
    results[model] = await getModelPermissions(model);
  }

  return results;
}

/**
 * Check if the current user has permission for a specific app and module combination
 */
export async function hasModulePermission(
  app: string,
  module: string,
  action: "view" | "create" | "edit" | "delete",
): Promise<boolean> {
  try {
    const session = await getServerSession();
    if (!session?.userId || !session?.tenantId) {
      return false;
    }

    const permissions = await getUserPermissions(
      session.userId,
      session.tenantId,
    );

    return checkModulePermission(permissions, app, module, action);
  } catch (error) {
    console.error("Error checking module permission:", error);
    return false;
  }
}

/**
 * Get all permissions for a specific app and module combination
 */
export async function getModulePermissions(
  app: string,
  module: string,
): Promise<{
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}> {
  try {
    const session = await getServerSession();
    if (!session?.userId || !session?.tenantId) {
      return {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      };
    }

    const permissions = await getUserPermissions(
      session.userId,
      session.tenantId,
    );

    return {
      canView: checkModulePermission(permissions, app, module, "view"),
      canCreate: checkModulePermission(permissions, app, module, "create"),
      canEdit: checkModulePermission(permissions, app, module, "edit"),
      canDelete: checkModulePermission(permissions, app, module, "delete"),
    };
  } catch (error) {
    console.error("Error getting module permissions:", error);
    return {
      canView: false,
      canCreate: false,
      canEdit: false,
      canDelete: false,
    };
  }
}

/**
 * Check if permissions allow a specific action on an app/module combination
 */
function checkModulePermission(
  permissions: UserPermissionMapping[],
  app: string,
  module: string,
  action: "view" | "create" | "edit" | "delete",
): boolean {
  // Find the permission for this app and module
  const permission = permissions.find(
    (p) => p.app === app && p.module === module,
  );

  if (!permission) {
    return false;
  }

  // Map action to permission property
  switch (action) {
    case "view":
      return permission.canView;
    case "create":
      return permission.canCreate;
    case "edit":
      return permission.canEdit;
    case "delete":
      return permission.canDelete;
    default:
      return false;
  }
}

/**
 * Get user's role information
 */
export async function getUserRole(): Promise<{
  name: string;
  isSystem: boolean;
} | null> {
  try {
    const session = await getServerSession();
    if (!session?.userId || !session?.tenantId) {
      return null;
    }

    const membership = await prisma.memberShip.findFirst({
      where: {
        userId: session.userId,
        tenantId: session.tenantId,
      },
      include: {
        customRole: {
          select: {
            name: true,
            isSystem: true,
          },
        },
      },
    });

    if (!membership?.customRole) {
      return null;
    }

    return {
      name: membership.customRole.name,
      isSystem: membership.customRole.isSystem,
    };
  } catch (error) {
    console.error("Error getting user role:", error);
    return null;
  }
}

/**
 * Check if current user can access a specific tenant
 */
export async function canAccessTenant(
  targetTenantId: string,
): Promise<boolean> {
  try {
    const session = await getServerSession();
    if (!session?.userId) {
      return false;
    }

    const membership = await prisma.memberShip.findFirst({
      where: {
        userId: session.userId,
        tenantId: targetTenantId,
      },
    });

    return !!membership;
  } catch (error) {
    console.error("Error checking tenant access:", error);
    return false;
  }
}

/**
 * Get all tenants the current user has access to
 */
export async function getUserTenants(): Promise<
  Array<{ id: string; name: string; role: string }>
> {
  try {
    const session = await getServerSession();
    if (!session?.userId) {
      return [];
    }

    const memberships = await prisma.memberShip.findMany({
      where: {
        userId: session.userId,
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
          },
        },
        customRole: {
          select: {
            name: true,
          },
        },
      },
    });

    return memberships.map((membership) => ({
      id: membership.tenant.id,
      name: membership.tenant.name || "Unnamed Organization",
      role: membership.customRole?.name || "No Role",
    }));
  } catch (error) {
    console.error("Error getting user tenants:", error);
    return [];
  }
}
