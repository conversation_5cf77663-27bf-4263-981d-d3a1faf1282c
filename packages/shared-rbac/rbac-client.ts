import { PrismaClient } from "@prisma/client";
import { applyRBAC } from "prisma-rbac";
import { prisma } from "@flinkk/database/prisma";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import {
  convertPermissionsToRBACFormat,
  getRestrictedModels,
  getModelSynonyms,
  getAllowedActions,
} from "./rbac-config";
import { RBACConfig, RBACUserSession, UserPermissionMapping } from "./types";
import { getTranslationFunction } from "./translations";

/**
 * Create an RBAC-enhanced Prisma client for the current user session
 */
export async function createRBACPrismaClient(): Promise<PrismaClient> {
  try {
    // Get current user session
    const session = await getServerSession();

    if (!session?.userId || !session?.tenantId) {
      // Return a restricted client for unauthenticated users
      return createPublicPrismaClient();
    }

    // Get user permissions from database
    const userPermissions = await getUserPermissions(
      session.userId,
      session.tenantId,
    );

    if (!userPermissions || userPermissions.length === 0) {
      // Return a restricted client for users without permissions
      return createRestrictedPrismaClient();
    }

    // Convert permissions to RBAC format
    const rbacPermissions = convertPermissionsToRBACFormat(userPermissions);

    // Create RBAC configuration
    const rbacConfig: RBACConfig = {
      permissions: rbacPermissions,
      restrictedModels: getRestrictedModels(),
      allowedActions: getAllowedActions(),
      synonyms: getModelSynonyms(),
      mismatchHandler: handlePermissionMismatch,
      translate: translateRBACError,
    };

    // Apply RBAC to Prisma client
    return applyRBAC({
      prismaClient: prisma,
      ...rbacConfig,
    });
  } catch (error) {
    console.error("Error creating RBAC Prisma client:", error);
    // Return a restricted client on error
    return createRestrictedPrismaClient();
  }
}

/**
 * Create an RBAC-enhanced Prisma client for a specific user
 */
export async function createRBACPrismaClientForUser(
  userId: string,
  tenantId: string,
): Promise<PrismaClient> {
  try {
    // Get user permissions from database
    const userPermissions = await getUserPermissions(userId, tenantId);

    if (!userPermissions || userPermissions.length === 0) {
      return createRestrictedPrismaClient();
    }

    // Convert permissions to RBAC format
    const rbacPermissions = convertPermissionsToRBACFormat(userPermissions);

    // Create RBAC configuration
    const rbacConfig: RBACConfig = {
      permissions: rbacPermissions,
      restrictedModels: getRestrictedModels(),
      allowedActions: getAllowedActions(),
      synonyms: getModelSynonyms(),
      mismatchHandler: handlePermissionMismatch,
      translate: translateRBACError,
    };

    // Apply RBAC to Prisma client
    return applyRBAC({
      prismaClient: prisma,
      ...rbacConfig,
    });
  } catch (error) {
    console.error("Error creating RBAC Prisma client for user:", error);
    return createRestrictedPrismaClient();
  }
}

/**
 * Get user permissions from database
 */
async function getUserPermissions(
  userId: string,
  tenantId: string,
): Promise<UserPermissionMapping[]> {
  try {
    const membership = await prisma.memberShip.findFirst({
      where: {
        userId,
        tenantId,
      },
      include: {
        customRole: {
          select: {
            name: true,
            permissions: {
              select: {
                app: true,
                module: true,
                canView: true,
                canCreate: true,
                canEdit: true,
                canDelete: true,
              },
            },
          },
        },
      },
    });

    if (!membership || !membership.customRole) {
      return [];
    }

    return membership.customRole.permissions;
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return [];
  }
}

/**
 * Create a public Prisma client with minimal permissions
 */
function createPublicPrismaClient(): PrismaClient {
  const rbacConfig: RBACConfig = {
    permissions: null,
    restrictedModels: getRestrictedModels(),
    allowedActions: [
      "session:create",
      "user:create:public",
      "verificationToken:create",
      "conversation:create:public",
      "message:create:public",
    ],
    synonyms: getModelSynonyms(),
    mismatchHandler: handlePermissionMismatch,
    translate: translateRBACError,
  };

  return applyRBAC({
    prismaClient: prisma,
    ...rbacConfig,
  });
}

/**
 * Create a restricted Prisma client with no permissions
 */
function createRestrictedPrismaClient(): PrismaClient {
  const rbacConfig: RBACConfig = {
    permissions: {},
    restrictedModels: getRestrictedModels(),
    allowedActions: [],
    synonyms: getModelSynonyms(),
    mismatchHandler: handlePermissionMismatch,
    translate: translateRBACError,
  };

  return applyRBAC({
    prismaClient: prisma,
    ...rbacConfig,
  });
}

/**
 * Handle permission mismatches
 */
function handlePermissionMismatch(
  missing: string[],
  redundant: string[],
): void {
  if (missing.length > 0) {
    console.warn("Missing permissions detected:", missing);
  }
  if (redundant.length > 0) {
    console.warn("Redundant permissions detected:", redundant);
  }
}

/**
 * Translate RBAC error messages with enhanced i18n support
 */
function translateRBACError(
  key: string,
  options?: Record<string, unknown>,
): string {
  // Get translation function based on user's locale (can be enhanced to get from session/headers)
  const translate = getTranslationFunction("en"); // Default to English, can be made dynamic
  return translate(key as any, options);
}
