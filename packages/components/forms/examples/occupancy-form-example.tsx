"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@flinkk/components/ui/card";
import { Loader2Icon } from "lucide-react";
import {
  OccupancyFields,
  createOccupancySchema,
  transformOccupancyData
} from "@flinkk/components/forms/occupancy-fields";
import { useOccupancyConfig } from "@flinkk/hooks/query/use-occupancy-config";
import { OccupancyConfig } from "@flinkk/inventory-api";

interface BookingFormData {
  // Other booking fields
  guestName: string;
  checkIn: string;
  checkOut: string;
  // Dynamic occupancy fields will be added with prefix "occupancy_"
  [key: string]: any;
}

interface OccupancyFormExampleProps {
  hotelId: string;
  onSubmit: (data: any) => Promise<void> | void;
  initialValues?: Record<string, any>;
}

export function OccupancyFormExample({
  hotelId,
  onSubmit,
  initialValues = {},
}: OccupancyFormExampleProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formSchema, setFormSchema] = useState<z.ZodSchema<any> | null>(null);

  // Fetch occupancy configs to build schema
  const {
    data: occupancyData,
    isLoading: isLoadingConfigs,
  } = useOccupancyConfig({
    hotelId,
    enabled: !!hotelId,
  });

  const occupancyConfigs: OccupancyConfig[] = (occupancyData as any)?.occupancy_configs || [];

  // Create dynamic schema when configs are loaded
  useEffect(() => {
    if (occupancyConfigs.length > 0) {
      const baseSchema = {
        guestName: z.string().min(1, "Guest name is required"),
        checkIn: z.string().min(1, "Check-in date is required"),
        checkOut: z.string().min(1, "Check-out date is required"),
      };

      const occupancySchemaFields = createOccupancySchema(occupancyConfigs);
      
      setFormSchema(z.object({
        ...baseSchema,
        ...occupancySchemaFields,
      }));
    }
  }, [occupancyConfigs]);

  // Initialize form
  const form = useForm<BookingFormData>({
    resolver: formSchema ? zodResolver(formSchema) : undefined,
    defaultValues: {
      guestName: initialValues.guestName || "",
      checkIn: initialValues.checkIn || "",
      checkOut: initialValues.checkOut || "",
      // Initialize occupancy fields
      ...occupancyConfigs.reduce((acc, config) => {
        acc[`occupancy_${config.id}`] = initialValues[`occupancy_${config.id}`] || 0;
        return acc;
      }, {} as Record<string, number>),
    },
  });

  // Reset form when configs change
  useEffect(() => {
    if (occupancyConfigs.length > 0) {
      const defaultValues = {
        guestName: initialValues.guestName || "",
        checkIn: initialValues.checkIn || "",
        checkOut: initialValues.checkOut || "",
        ...occupancyConfigs.reduce((acc, config) => {
          acc[`occupancy_${config.id}`] = initialValues[`occupancy_${config.id}`] || 0;
          return acc;
        }, {} as Record<string, number>),
      };
      
      form.reset(defaultValues);
    }
  }, [occupancyConfigs, initialValues, form]);

  const handleSubmit = async (data: BookingFormData) => {
    setIsSubmitting(true);
    
    try {
      // Transform occupancy data to API format
      const occupancyPayload = transformOccupancyData(data, occupancyConfigs);
      
      // Prepare final payload
      const payload = {
        guestName: data.guestName,
        checkIn: data.checkIn,
        checkOut: data.checkOut,
        occupancy: occupancyPayload,
      };

      console.log("Submitting booking with occupancy:", payload);
      await onSubmit(payload);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingConfigs) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2Icon className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading form configuration...</span>
        </CardContent>
      </Card>
    );
  }

  if (!formSchema) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">
            Unable to load form configuration.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Hotel Booking Form</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic booking fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Guest Name *</label>
                <input
                  {...form.register("guestName")}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Enter guest name"
                />
                {form.formState.errors.guestName && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.guestName.message}
                  </p>
                )}
              </div>
              
              <div>
                <label className="text-sm font-medium">Check-in Date *</label>
                <input
                  {...form.register("checkIn")}
                  type="date"
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                />
                {form.formState.errors.checkIn && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.checkIn.message}
                  </p>
                )}
              </div>
              
              <div>
                <label className="text-sm font-medium">Check-out Date *</label>
                <input
                  {...form.register("checkOut")}
                  type="date"
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                />
                {form.formState.errors.checkOut && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.checkOut.message}
                  </p>
                )}
              </div>
            </div>

            {/* Dynamic occupancy fields */}
            <OccupancyFields
              hotelId={hotelId}
              control={form.control}
              disabled={isSubmitting}
              title="Guest Occupancy"
              showTitle={true}
            />

            {/* Submit button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2Icon className="h-4 w-4 animate-spin mr-2" />
                    Submitting...
                  </>
                ) : (
                  "Submit Booking"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
