"use server";

import { prisma } from "@flinkk/database/prisma";
import { unstable_noStore } from "next/cache";
import { getServerSession } from "@flinkk/shared-auth/server-session";

export type BaseQueryInput = {
  page?: number;
  perPage?: number;
  sort?: Array<{ id: string; desc: boolean }>;
  filters?: any[];
  createdAt?: number[];
  tableFields?: string[];
};

export async function baseQuery(
  model: string,
  input: BaseQueryInput,
  whereConditions: any[] = [],
) {
  unstable_noStore();
  try {
    const { tenantId, userId } = await getServerSession();

    if (!tenantId) {
      throw new Error("Unauthorized: No tenant ID found");
    }

    // Ensure valid pagination values
    const page = Math.max(1, Number(input.page) || 1);
    const perPage = Math.max(1, Number(input.perPage) || 25);
    const skip = (page - 1) * perPage;

    // Build where conditions
    let whereConditionsWithBranch = [
      ...whereConditions,
      { tenantId: tenantId },
    ].filter(Boolean);

    const where = {
      AND: whereConditionsWithBranch,
    };

    // Build orderBy with proper handling of relation fields
    const orderBy = input.sort?.length
      ? input.sort.map((item) => {
          const [root, nested] = item.id.split(".");

          // Handle nested relation fields (e.g., "user.name")
          if (nested) {
            return {
              [root]: {
                [nested]: item.desc ? "desc" : "asc",
              },
            };
          }

          // Handle direct fields
          return {
            [item.id]: item.desc ? "desc" : "asc",
          };
        })
      : [{ createdAt: "desc" }];

    // Build select
    let select = input.tableFields?.length
      ? input.tableFields.reduce((acc: any, field: string) => {
          const parts = field.split(".");

          if (parts.length > 2) {
            // Handle nested relations (e.g., "patient.user.name")
            const [root, nested, nestedField] = parts;
            acc[root] = acc[root] || { select: {} };
            acc[root].select[nested] = {
              select: {
                [nestedField]: true,
              },
            };
          } else if (parts.length === 2) {
            const [root, nested] = parts;
            if (nested === "user.name") {
              // Special handling for user.name
              acc[root] = acc[root] || { select: {} };
              acc[root].select["user"] = {
                select: {
                  name: true,
                },
              };
            } else {
              acc[root] = acc[root] || { select: {} };
              acc[root].select[nested] = true;
            }
          } else {
            // Handle direct fields
            acc[parts[0]] = true;
          }
          return acc;
        }, {})
      : undefined;

    const queryOptions = {
      where,
      take: perPage,
      skip,
      orderBy,
      ...(select && Object.keys(select).length > 0 ? { select } : {}),
    };

    // Execute query with transaction
    let [data, total] = await prisma.$transaction([
      prisma[model].findMany(queryOptions),
      prisma[model].count({ where }),
    ]);

    return {
      data,
      pageCount: Math.ceil(total / perPage),
    };
  } catch (error) {
    console.error(`Error in ${model} query:`, error);
    throw error; // Re-throw the error for proper error handling
  }
}
