import { useQuery } from "@tanstack/react-query";
import { OccupancyConfig } from "@flinkk/inventory-api";

interface OccupancyConfigResponse {
  occupancy_configs: OccupancyConfig[];
}

interface UseOccupancyConfigParams {
  hotelId: string;
  enabled?: boolean;
}

export function useOccupancyConfig({ hotelId, enabled = true }: UseOccupancyConfigParams) {
  return useQuery<OccupancyConfigResponse>({
    queryKey: ["occupancy-config", hotelId],
    queryFn: async (): Promise<OccupancyConfigResponse> => {
      const response = await fetch(
        `/api/hotel-management/occupancy-config?hotel_id=${hotelId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch occupancy config");
      }

      return response.json() as Promise<OccupancyConfigResponse>;
    },
    enabled: enabled && !!hotelId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}
