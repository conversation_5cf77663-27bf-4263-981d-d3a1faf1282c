import { getOpportunityLines } from "./actions";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { OpportunityLinesClient } from "./page-client";

export default async function OpportunityLinesPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  const { tenantId, userId } = await getServerSession();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const opportunityId = params.id;

  // Fetch opportunity lines for this opportunity
  const linesData = await getOpportunityLines({
    opportunityId,
    page: 1,
    perPage: 50,
  });

  return (
    <OpportunityLinesClient
      opportunityId={opportunityId}
      initialData={linesData}
      userId={userId}
    />
  );
}
