import { PageClient } from "@flinkk/shared-slots-whatsapp/page-client";
import { getServerSession } from "@flinkk/shared-auth/server-session";

export default async function OpportunitiesWhatsAppPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;

  const { userId } = await getServerSession();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const opportunityId = params.id;

  return (
    <PageClient
      entityType="Opportunity"
      entityId={opportunityId}
      userId={userId}
    />
  );
}
