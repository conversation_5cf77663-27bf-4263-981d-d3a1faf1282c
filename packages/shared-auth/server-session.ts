import { getServerSession as _nextAuthGetServerSession } from "next-auth";
import { authOptions } from "./options";
import { CustomSession } from "./options";

const getServerSession = async (retryCount = 0) => {
  const maxRetries = 2;

  try {
    const session = (await _nextAuthGetServerSession(
      authOptions,
    )) as CustomSession;

    if (!session) {
      console.warn("Server session retrieval failed", {
        retryCount,
        timestamp: new Date().toISOString(),
      });

      // Retry logic for intermittent session failures
      if (retryCount < maxRetries) {
        await new Promise((resolve) =>
          setTimeout(resolve, 100 * (retryCount + 1)),
        );
        return getServerSession(retryCount + 1);
      }

      throw new Error("Unauthorized");
    }

    if (!session.userId || !session.tenantId) {
      console.warn("Server session missing required fields", {
        hasUserId: !!session.userId,
        hasTenantId: !!session.tenantId,
        sessionKeys: Object.keys(session),
        retryCount,
        timestamp: new Date().toISOString(),
      });

      // Retry logic for incomplete session data
      if (retryCount < maxRetries) {
        await new Promise((resolve) =>
          setTimeout(resolve, 100 * (retryCount + 1)),
        );
        return getServerSession(retryCount + 1);
      }

      throw new Error("Unauthorized");
    }

    return { userId: session.userId, tenantId: session.tenantId };
  } catch (error) {
    console.error("Error in getServerSession function", {
      error: error.message,
      retryCount,
      timestamp: new Date().toISOString(),
      stack: error.stack,
    });

    throw error;
  }
};

export { getServerSession };
