import { Prisma } from "@prisma/client";
import { getContext } from "../context";

/**
 * Webhook Trigger Middleware
 *
 * This middleware automatically triggers webhooks for database operations.
 * Key improvements:
 *
 * 1. **Tenant/User Resolution from Query Results**: Instead of relying solely on context,
 *    the middleware now extracts tenant ID and user ID directly from the query results.
 *    This ensures webhooks are triggered with correct tenant information even when
 *    context is missing or incorrect.
 *
 * 2. **Pre-Delete Entity Capture**: For delete operations, the middleware captures
 *    entity data before deletion to ensure webhook payloads contain complete information.
 *
 * 3. **Relationship-Based Tenant Resolution**: For models without direct tenantId fields,
 *    the middleware attempts to resolve tenant information through relationships.
 *
 * 4. **Fallback to Context**: When tenant/user information cannot be extracted from
 *    query results, the middleware falls back to context as before.
 */

/**
 * Configuration options for webhook trigger middleware
 */
export interface WebhookTriggerOptions {
  /** Whether to require context (userId, tenantId) from async local storage */
  requireContext?: boolean;
  /** Default tenant ID to use when context is not available */
  defaultTenantId?: string;
  /** Whether to skip webhook triggers when context is missing */
  skipOnMissingContext?: boolean;
  /** Whether to allow system-triggered webhooks (without user context) */
  allowSystemTriggers?: boolean;
}

/**
 * Webhook event mapping for different models and operations
 * This maps Prisma model names to their corresponding webhook events
 */
const WEBHOOK_EVENT_MAP = {
  // CRM Events
  Lead: {
    create: "lead.created",
    update: "lead.updated",
    delete: "lead.deleted",
  },
  Contact: {
    create: "contact.created",
    update: "contact.updated",
    delete: "contact.deleted",
  },
  BusinessAccount: {
    create: "account.created",
    update: "account.updated",
    delete: "account.deleted",
  },
  Opportunity: {
    create: "opportunity.created",
    update: "opportunity.updated",
    delete: "opportunity.deleted",
  },
  Task: {
    create: "task.created",
    update: "task.updated",
    delete: "task.deleted",
  },
  Campaign: {
    create: "campaign.created",
    update: "campaign.updated",
    delete: "campaign.deleted",
  },
  // Support Events
  SupportTicket: {
    create: "ticket.created",
    update: "ticket.updated",
    delete: "ticket.deleted",
  },
  Note: {
    create: "ticket.note_added",
    update: "note.updated",
    delete: "note.deleted",
  },
  TicketAttachment: {
    create: "ticket.attachment_added",
    update: "attachment.updated",
    delete: "attachment.deleted",
  },
  // Sales Events
  Quote: {
    create: "quote.created",
    update: "quote.updated",
    delete: "quote.deleted",
  },
  Product: {
    create: "product.created",
    update: "product.updated",
    delete: "product.deleted",
  },
  // System Events
  User: {
    create: "user.created",
    update: "user.updated",
    delete: "user.deleted",
  },
  ApiKey: {
    create: "api_key.created",
    update: "api_key.updated",
    delete: "api_key.deleted",
  },
  WebhookConfig: {
    create: "webhook.created",
    update: "webhook.updated",
    delete: "webhook.deleted",
  },
} as const;

/**
 * Additional webhook events that can be triggered manually for specific business logic
 * These events are not automatically triggered by CRUD operations but can be fired
 * when specific conditions are met (e.g., status changes, assignments, etc.)
 */
export const ADDITIONAL_WEBHOOK_EVENTS = {
  // Lead Events
  "lead.status_changed": "When a lead status changes",
  "lead.stage_changed": "When a lead stage changes",
  "lead.converted": "When a lead is converted to opportunity",
  "lead.assigned": "When a lead is assigned to a user",
  "lead.score_changed": "When a lead score changes",

  // Opportunity Events
  "opportunity.stage_changed": "When an opportunity stage changes",
  "opportunity.won": "When an opportunity is won",
  "opportunity.lost": "When an opportunity is lost",
  "opportunity.assigned": "When an opportunity is assigned to a user",
  "opportunity.amount_changed": "When opportunity amount changes",
  "opportunity.probability_changed": "When opportunity probability changes",

  // Contact Events
  "contact.assigned": "When a contact is assigned to a user",
  "contact.tagged": "When tags are added to a contact",

  // Account Events
  "account.assigned": "When an account is assigned to a user",

  // Task Events
  "task.completed": "When a task is marked as completed",
  "task.assigned": "When a task is assigned to a user",
  "task.due_date_changed": "When a task due date changes",
  "task.priority_changed": "When a task priority changes",
  "task.overdue": "When a task becomes overdue",

  // Ticket Events
  "ticket.status_changed": "When a ticket status changes",
  "ticket.priority_changed": "When a ticket priority changes",
  "ticket.resolved": "When a ticket is resolved",
  "ticket.closed": "When a ticket is closed",
  "ticket.reopened": "When a ticket is reopened",
  "ticket.note_added": "When a note is added to a ticket",
  "ticket.attachment_added": "When an attachment is added to a ticket",
  "ticket.escalated": "When a ticket is escalated",
  "ticket.sla_violated": "When a ticket violates SLA",
  "ticket.first_response": "When a ticket receives first response",

  // Campaign Events
  "campaign.started": "When a campaign is started",
  "campaign.completed": "When a campaign is completed",
  "campaign.paused": "When a campaign is paused",

  // Quote Events
  "quote.sent": "When a quote is sent to customer",
  "quote.accepted": "When a quote is accepted by customer",
  "quote.rejected": "When a quote is rejected by customer",
  "quote.expired": "When a quote expires",

  // Product Events
  "product.price_changed": "When a product price changes",
  "product.stock_low": "When product stock is low",

  // System Events
  "user.login": "When a user logs in",
  "user.logout": "When a user logs out",
  "api_key.updated": "When an API key is updated",
  "webhook.updated": "When a webhook is updated",
} as const;

/**
 * Manually trigger a webhook event for specific business logic
 * Use this for events that are not automatically triggered by CRUD operations
 *
 * @param tenantId - The tenant ID
 * @param eventType - The webhook event type (from ADDITIONAL_WEBHOOK_EVENTS)
 * @param data - The data to send with the webhook
 * @param changes - Optional changes object for update events
 */
export async function triggerManualWebhookEvent(
  tenantId: string,
  eventType: keyof typeof ADDITIONAL_WEBHOOK_EVENTS,
  data: any,
  changes?: Record<string, { from: any; to: any }>,
): Promise<void> {
  try {
    // Use the same triggerWebhooks function as the middleware
    await triggerWebhooks(tenantId, eventType, data, changes);
  } catch (error) {
    console.error(`Error triggering manual webhook event ${eventType}:`, error);
    // Don't throw error to avoid breaking the original operation
  }
}

/**
 * Get all available webhook events (both automatic and manual)
 */
export function getAllWebhookEvents(): string[] {
  const automaticEvents = Object.values(WEBHOOK_EVENT_MAP).flatMap((events) =>
    Object.values(events),
  );
  const manualEvents = Object.keys(ADDITIONAL_WEBHOOK_EVENTS);

  return [...automaticEvents, ...manualEvents].sort();
}

/**
 * Check if a webhook event is supported
 */
export function isWebhookEventSupported(eventType: string): boolean {
  const allEvents = getAllWebhookEvents();
  return allEvents.includes(eventType);
}

/**
 * Extract webhook data from entity result based on model type
 */
function extractWebhookData(model: string, entityResult: any) {
  const baseData = {
    id: entityResult?.id,
    createdAt: entityResult?.createdAt,
    updatedAt: entityResult?.updatedAt,
  };

  // Add model-specific data
  switch (model) {
    case "Lead":
      return {
        ...baseData,
        firstName: entityResult?.firstName,
        lastName: entityResult?.lastName,
        email: entityResult?.email,
        phone: entityResult?.phone,
        company: entityResult?.company,
        status: entityResult?.status,
        source: entityResult?.source,
        assigneeId: entityResult?.assigneeId,
      };

    case "Contact":
      return {
        ...baseData,
        firstName: entityResult?.firstName,
        lastName: entityResult?.lastName,
        email: entityResult?.email,
        phoneNumber: entityResult?.phoneNumber,
        accountId: entityResult?.accountId,
      };

    case "BusinessAccount":
      return {
        ...baseData,
        name: entityResult?.name,
        website: entityResult?.website,
        industry: entityResult?.industry,
        size: entityResult?.size,
        assigneeId: entityResult?.assigneeId,
      };

    case "Opportunity":
      return {
        ...baseData,
        dealName: entityResult?.dealName,
        amount: entityResult?.amount,
        stage: entityResult?.stage,
        probability: entityResult?.probability,
        closeDate: entityResult?.closeDate,
        accountId: entityResult?.accountId,
        contactId: entityResult?.contactId,
        assigneeId: entityResult?.assigneeId,
      };

    case "Task":
      return {
        ...baseData,
        title: entityResult?.title,
        description: entityResult?.description,
        status: entityResult?.status,
        priority: entityResult?.priority,
        dueDate: entityResult?.dueDate,
        assigneeId: entityResult?.assigneeId,
        reference_modal: entityResult?.reference_modal,
        reference_modalId: entityResult?.reference_modalId,
      };

    case "SupportTicket":
      return {
        ...baseData,
        ticketNumber: entityResult?.ticketNumber,
        subject: entityResult?.subject,
        description: entityResult?.description,
        status: entityResult?.status,
        priority: entityResult?.priority,
        type: entityResult?.type,
        tags: entityResult?.tags,
        channel: entityResult?.channel,
        contactId: entityResult?.contactId,
        assigneeId: entityResult?.assigneeId,
      };

    case "Campaign":
      return {
        ...baseData,
        name: entityResult?.name,
        description: entityResult?.description,
        status: entityResult?.status,
        type: entityResult?.type,
        startDate: entityResult?.startDate,
        endDate: entityResult?.endDate,
      };

    case "Quote":
      return {
        ...baseData,
        quoteNumber: entityResult?.quoteNumber,
        title: entityResult?.title,
        status: entityResult?.status,
        totalAmount: entityResult?.totalAmount,
        validUntil: entityResult?.validUntil,
        contactId: entityResult?.contactId,
        opportunityId: entityResult?.opportunityId,
      };

    case "Product":
      return {
        ...baseData,
        name: entityResult?.name,
        description: entityResult?.description,
        sku: entityResult?.sku,
        price: entityResult?.price,
        category: entityResult?.category,
        isActive: entityResult?.isActive,
      };

    case "User":
      return {
        ...baseData,
        name: entityResult?.name,
        email: entityResult?.email,
        role: entityResult?.role,
      };

    case "ApiKey":
      return {
        ...baseData,
        name: entityResult?.name,
        keyPrefix: entityResult?.keyPrefix,
        isActive: entityResult?.isActive,
        expiresAt: entityResult?.expiresAt,
      };

    case "WebhookConfig":
      return {
        ...baseData,
        name: entityResult?.name,
        url: entityResult?.url,
        events: entityResult?.events,
        isActive: entityResult?.isActive,
      };

    case "Note":
      return {
        ...baseData,
        title: entityResult?.title,
        content: entityResult?.content,
        reference_modal: entityResult?.reference_modal,
        reference_modalId: entityResult?.reference_modalId,
        authorId: entityResult?.authorId,
      };

    case "TicketAttachment":
      return {
        ...baseData,
        fileName: entityResult?.fileName,
        originalName: entityResult?.originalName,
        fileSize: entityResult?.fileSize,
        mimeType: entityResult?.mimeType,
        fileUrl: entityResult?.fileUrl,
        isPublic: entityResult?.isPublic,
        ticketId: entityResult?.ticketId,
        uploadedById: entityResult?.uploadedById,
      };

    default:
      return baseData;
  }
}

/**
 * Deliver a single webhook
 */
async function deliverWebhook(
  webhook: any,
  eventType: string,
  data: any,
  changes?: Record<string, { from: any; to: any }>,
  prismaInstance?: any,
): Promise<void> {
  const payload = {
    event: eventType,
    timestamp: new Date().toISOString(),
    data: {
      ...data,
      ...(changes && { changes }),
    },
    tenant: {
      id: webhook.tenant.id,
      name: webhook.tenant.name,
    },
  };

  // Create delivery record
  const delivery = await prismaInstance.webhookDelivery.create({
    data: {
      webhookId: webhook.id,
      eventType,
      payload: payload as any,
      status: "PENDING",
      tenantId: webhook.tenantId,
    },
  });

  try {
    const result = await sendWebhookRequest(webhook, payload);

    console.log("Webhook delivery result:", result);

    // Update delivery record with result
    await prismaInstance.webhookDelivery.update({
      where: { id: delivery.id },
      data: {
        status: result.success ? "SUCCESS" : "FAILED",
        httpStatus: result.httpStatus,
        responseBody: result.responseBody,
        responseTime: result.responseTime,
        errorMessage: result.errorMessage,
      },
    });

    // If failed and retries are enabled, schedule retry
    if (!result.success && webhook.retryEnabled) {
      await scheduleRetry(delivery.id, webhook, prismaInstance);
    }
  } catch (error) {
    console.error("Webhook delivery error:", error);

    // Update delivery record with error
    await prismaInstance.webhookDelivery.update({
      where: { id: delivery.id },
      data: {
        status: "FAILED",
        errorMessage: error instanceof Error ? error.message : String(error),
      },
    });

    // Schedule retry if enabled
    if (webhook.retryEnabled) {
      await scheduleRetry(delivery.id, webhook, prismaInstance);
    }
  }
}

/**
 * Send the actual HTTP request to the webhook endpoint
 */
async function sendWebhookRequest(
  webhook: any,
  payload: any,
): Promise<{
  success: boolean;
  httpStatus?: number;
  responseBody?: string;
  responseTime: number;
  errorMessage?: string;
}> {
  const startTime = Date.now();

  try {
    // Prepare headers
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "User-Agent": "Flinkk-Webhooks/1.0",
      "X-Webhook-Event": payload.event,
      "X-Webhook-Timestamp": payload.timestamp,
      "X-Webhook-Tenant": payload.tenant.id,
    };

    // Add custom headers if configured
    if (webhook.headers) {
      Object.assign(headers, webhook.headers);
    }

    // Add signature if secret is configured
    if (webhook.secret) {
      const { createHmac } = await import("crypto");
      const hmac = createHmac("sha256", webhook.secret);
      hmac.update(JSON.stringify(payload));
      const signature = `sha256=${hmac.digest("hex")}`;
      headers["X-Webhook-Signature"] = signature;
    }

    // Send the request
    const response = await fetch(webhook.url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(30000), // 30 second timeout
    });

    const responseTime = Date.now() - startTime;
    const responseBody = await response.text();

    return {
      success: response.ok,
      httpStatus: response.status,
      responseBody: responseBody.substring(0, 1000), // Limit response body size
      responseTime,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;

    return {
      success: false,
      responseTime,
      errorMessage: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Schedule a retry for a failed webhook delivery
 */
async function scheduleRetry(
  deliveryId: string,
  webhook: any,
  prismaInstance: any,
): Promise<void> {
  try {
    // Get current retry count
    const delivery = await prismaInstance.webhookDelivery.findUnique({
      where: { id: deliveryId },
      select: { retryCount: true },
    });

    const retryCount = (delivery?.retryCount || 0) + 1;

    // Check if we've exceeded max retries
    if (retryCount > webhook.maxRetries) {
      await prismaInstance.webhookDelivery.update({
        where: { id: deliveryId },
        data: { status: "FAILED_PERMANENTLY" },
      });
      return;
    }

    // Calculate next retry time (exponential backoff)
    const baseDelay = webhook.retryDelay || 60; // Default 60 seconds
    const delay = baseDelay * Math.pow(2, retryCount - 1); // Exponential backoff
    const nextRetryAt = new Date(Date.now() + delay * 1000);

    // Update delivery record with retry info
    await prismaInstance.webhookDelivery.update({
      where: { id: deliveryId },
      data: {
        retryCount,
        nextRetryAt,
        status: "PENDING_RETRY",
      },
    });
  } catch (error) {
    console.error("Error scheduling webhook retry:", error);
  }
}

/**
 * Trigger webhooks for a specific event
 */
async function triggerWebhooks(
  tenantId: string,
  eventType: string,
  data: any,
  changes?: Record<string, { from: any; to: any }>,
): Promise<void> {
  try {
    console.log("Triggering webhooks for", eventType, "in", tenantId);
    // Use a separate prisma instance to find and trigger webhooks
    const { PrismaClient } = await import("@prisma/client");
    const webhookPrisma = new PrismaClient();

    try {
      // Find all active webhooks for this tenant that listen to this event
      const webhooks = await webhookPrisma.webhookConfig.findMany({
        where: {
          tenantId,
          isActive: true,
          events: {
            has: eventType,
          },
        },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      console.log("Webhooks found:", webhooks);

      // Send webhooks in parallel
      const deliveryPromises = webhooks.map((webhook) =>
        deliverWebhook(webhook, eventType, data, changes, webhookPrisma),
      );

      const fin: any = await Promise.allSettled(deliveryPromises);
      console.log("Webhook deliveries:", fin);
      return fin;
    } finally {
      await webhookPrisma.$disconnect();
    }
  } catch (error) {
    console.error(`Error triggering webhooks for ${eventType}:`, error);
    // Don't throw error to avoid breaking the original operation
  }
}

/**
 * Detect changes between old and new data for update operations
 * Also triggers additional webhook events based on field changes
 */
function detectChanges(
  args: any,
  preUpdateEntity?: any,
): Record<string, { from: any; to: any }> | undefined {
  // For update operations, we can detect changes from the args.data
  const updateData = args.data;
  if (!updateData || typeof updateData !== "object") {
    return undefined;
  }

  const changes: Record<string, { from: any; to: any }> = {};

  // Compare with previous values if available
  Object.keys(updateData).forEach((key) => {
    if (updateData[key] !== undefined) {
      const oldValue = preUpdateEntity?.[key];
      const newValue = updateData[key];

      // Only record as change if values are actually different
      if (oldValue !== newValue) {
        changes[key] = {
          from: oldValue,
          to: newValue,
        };
      }
    }
  });

  return Object.keys(changes).length > 0 ? changes : undefined;
}

/**
 * Trigger additional webhook events based on field changes
 * This function checks for specific field changes and triggers corresponding events
 */
async function triggerAdditionalEvents(
  model: string,
  changes: Record<string, { from: any; to: any }>,
  tenantId: string,
  entityData: any,
): Promise<void> {
  if (!changes || Object.keys(changes).length === 0) {
    return;
  }

  const additionalEvents: Array<{ eventType: string; data: any }> = [];

  // Handle general soft delete detection for all models
  if (changes.deleted && changes.deleted.to === true) {
    // Get the base event type for this model
    const eventMap = WEBHOOK_EVENT_MAP[model as keyof typeof WEBHOOK_EVENT_MAP];
    if (eventMap && eventMap.delete) {
      additionalEvents.push({
        eventType: eventMap.delete,
        data: {
          ...entityData,
          deletedAt: new Date().toISOString(),
        },
      });
    }
  }

  // Handle SupportTicket specific events
  if (model === "SupportTicket") {
    // Status changes
    if (changes.status) {
      additionalEvents.push({
        eventType: "ticket.status_changed",
        data: {
          ...entityData,
          statusChange: {
            from: changes.status.from,
            to: changes.status.to,
          },
        },
      });

      // Specific status events
      if (changes.status.to === "RESOLVED") {
        additionalEvents.push({
          eventType: "ticket.resolved",
          data: entityData,
        });
      } else if (changes.status.to === "CLOSED") {
        additionalEvents.push({
          eventType: "ticket.closed",
          data: entityData,
        });
      } else if (
        changes.status.from === "CLOSED" &&
        changes.status.to !== "CLOSED"
      ) {
        additionalEvents.push({
          eventType: "ticket.reopened",
          data: entityData,
        });
      }
    }

    // Priority changes
    if (changes.priority) {
      additionalEvents.push({
        eventType: "ticket.priority_changed",
        data: {
          ...entityData,
          priorityChange: {
            from: changes.priority.from,
            to: changes.priority.to,
          },
        },
      });
    }
  }

  // Handle Lead specific events
  if (model === "Lead") {
    if (changes.status) {
      additionalEvents.push({
        eventType: "lead.status_changed",
        data: {
          ...entityData,
          statusChange: {
            from: changes.status.from,
            to: changes.status.to,
          },
        },
      });
    }

    if (changes.assigneeId) {
      additionalEvents.push({
        eventType: "lead.assigned",
        data: {
          ...entityData,
          assignmentChange: {
            from: changes.assigneeId.from,
            to: changes.assigneeId.to,
          },
        },
      });
    }
  }

  // Handle Opportunity specific events
  if (model === "Opportunity") {
    if (changes.stage) {
      additionalEvents.push({
        eventType: "opportunity.stage_changed",
        data: {
          ...entityData,
          stageChange: {
            from: changes.stage.from,
            to: changes.stage.to,
          },
        },
      });

      // Specific stage events
      if (changes.stage.to === "WON") {
        additionalEvents.push({
          eventType: "opportunity.won",
          data: entityData,
        });
      } else if (changes.stage.to === "LOST") {
        additionalEvents.push({
          eventType: "opportunity.lost",
          data: entityData,
        });
      }
    }

    if (changes.dealOwner || changes.assigneeId) {
      additionalEvents.push({
        eventType: "opportunity.assigned",
        data: {
          ...entityData,
          assignmentChange: {
            from: changes.dealOwner?.from || changes.assigneeId?.from,
            to: changes.dealOwner?.to || changes.assigneeId?.to,
          },
        },
      });
    }

    if (changes.amount) {
      additionalEvents.push({
        eventType: "opportunity.amount_changed",
        data: {
          ...entityData,
          amountChange: {
            from: changes.amount.from,
            to: changes.amount.to,
          },
        },
      });
    }
  }

  // Handle Task specific events
  if (model === "Task") {
    if (changes.status && changes.status.to === "COMPLETED") {
      additionalEvents.push({
        eventType: "task.completed",
        data: entityData,
      });
    }

    if (changes.assigneeId || changes.userId) {
      additionalEvents.push({
        eventType: "task.assigned",
        data: {
          ...entityData,
          assignmentChange: {
            from: changes.assigneeId?.from || changes.userId?.from,
            to: changes.assigneeId?.to || changes.userId?.to,
          },
        },
      });
    }

    if (changes.priority) {
      additionalEvents.push({
        eventType: "task.priority_changed",
        data: {
          ...entityData,
          priorityChange: {
            from: changes.priority.from,
            to: changes.priority.to,
          },
        },
      });
    }

    if (changes.dueDate) {
      additionalEvents.push({
        eventType: "task.due_date_changed",
        data: {
          ...entityData,
          dueDateChange: {
            from: changes.dueDate.from,
            to: changes.dueDate.to,
          },
        },
      });
    }
  }

  // Handle Campaign specific events
  if (model === "Campaign") {
    if (changes.status) {
      // Specific campaign status events
      if (changes.status.to === "ACTIVE") {
        additionalEvents.push({
          eventType: "campaign.started",
          data: entityData,
        });
      } else if (changes.status.to === "COMPLETED") {
        additionalEvents.push({
          eventType: "campaign.completed",
          data: entityData,
        });
      } else if (changes.status.to === "PAUSED") {
        additionalEvents.push({
          eventType: "campaign.paused",
          data: entityData,
        });
      }
    }
  }

  // Handle Contact specific events
  if (model === "Contact") {
    if (changes.userId) {
      additionalEvents.push({
        eventType: "contact.assigned",
        data: {
          ...entityData,
          assignmentChange: {
            from: changes.userId.from,
            to: changes.userId.to,
          },
        },
      });
    }

    if (changes.tags) {
      additionalEvents.push({
        eventType: "contact.tagged",
        data: {
          ...entityData,
          tagChange: {
            from: changes.tags.from,
            to: changes.tags.to,
          },
        },
      });
    }
  }

  // Handle BusinessAccount specific events
  if (model === "BusinessAccount") {
    if (changes.assigneeId || changes.userId) {
      additionalEvents.push({
        eventType: "account.assigned",
        data: {
          ...entityData,
          assignmentChange: {
            from: changes.assigneeId?.from || changes.userId?.from,
            to: changes.assigneeId?.to || changes.userId?.to,
          },
        },
      });
    }
  }

  // Trigger all additional events
  for (const event of additionalEvents) {
    try {
      await triggerManualWebhookEvent(
        tenantId,
        event.eventType as keyof typeof ADDITIONAL_WEBHOOK_EVENTS,
        event.data,
      );
    } catch (error) {
      console.error(
        `Error triggering additional webhook event ${event.eventType}:`,
        error,
      );
    }
  }
}

/**
 * Extract tenant ID and user ID from query result based on model type
 * For models without direct tenantId, resolve through relationships
 *
 * This function prioritizes getting tenant and user information from the actual
 * database query result rather than relying solely on context. This ensures
 * webhooks are triggered with the correct tenant and user information even
 * when context might be missing or incorrect.
 *
 * @param model - The Prisma model name
 * @param entityResult - The result from the database query
 * @param operation - The operation type (create, update, delete)
 * @param args - The query arguments
 * @param prismaInstance - The Prisma instance for additional queries if needed
 * @returns Object containing tenantId and userId if found
 */
async function extractTenantAndUserFromResult(
  model: string,
  entityResult: any,
  operation: string,
  _args: any, // Prefixed with underscore to indicate intentionally unused
  prismaInstance?: any,
): Promise<{ tenantId?: string; userId?: string }> {
  // For delete operations, we should have pre-delete entity data
  if (operation === "delete") {
    // If we don't have entity data (e.g., pre-delete fetch failed), fall back to context
    if (!entityResult) {
      return { tenantId: undefined, userId: undefined };
    }
    // Continue with normal processing using the pre-delete entity data
  }

  // Models with direct tenantId field
  const modelsWithDirectTenantId = [
    "Lead",
    "Contact",
    "BusinessAccount",
    "Opportunity",
    "Task",
    "Campaign",
    "SupportTicket",
    "Product",
    "Quote",
    "ApiKey",
    "WebhookConfig",
    "Note",
    "TicketAttachment",
  ];

  if (modelsWithDirectTenantId.includes(model)) {
    return {
      tenantId: entityResult?.tenantId,
      userId:
        entityResult?.userId ||
        entityResult?.createdById ||
        entityResult?.assigneeId ||
        entityResult?.dealOwner, // For Opportunity model
    };
  }

  // Handle models without direct tenantId - need to resolve through relationships
  switch (model) {
    case "User":
      // For User model, we need to get tenant through MemberShip
      // Since a user can belong to multiple tenants, we can't determine which tenant
      // without additional context. Return undefined to fall back to context.
      return { tenantId: undefined, userId: entityResult?.id };

    default:
      // For unknown models, try to resolve tenant through common relationship patterns
      return await resolveTenantThroughRelationships(
        model,
        entityResult,
        prismaInstance,
      );
  }
}

/**
 * Attempt to resolve tenant ID through common relationship patterns
 * This function handles models that don't have direct tenantId but may have
 * relationships to models that do have tenantId
 */
async function resolveTenantThroughRelationships(
  model: string,
  entityResult: any,
  prismaInstance?: any,
): Promise<{ tenantId?: string; userId?: string }> {
  if (!entityResult || !prismaInstance) {
    return { tenantId: undefined, userId: undefined };
  }

  // Common relationship patterns to check for tenant resolution
  const relationshipChecks = [
    // Check if entity has a userId that can be traced to a tenant
    {
      field: "userId",
      targetModel: "user",
      targetField: "membership",
    },
    // Check if entity has a contactId that can be traced to a tenant
    {
      field: "contactId",
      targetModel: "contact",
      targetField: "tenantId",
    },
    // Check if entity has an accountId that can be traced to a tenant
    {
      field: "accountId",
      targetModel: "businessAccount",
      targetField: "tenantId",
    },
    // Check if entity has an opportunityId that can be traced to a tenant
    {
      field: "opportunityId",
      targetModel: "opportunity",
      targetField: "tenantId",
    },
  ];

  for (const check of relationshipChecks) {
    const relationId = entityResult[check.field];
    if (relationId) {
      try {
        // This is a simplified approach - in a real implementation,
        // you might want to use proper Prisma queries
        // For now, return undefined to fall back to context
        console.log(
          `Found ${check.field} relationship for ${model}, but tenant resolution through relationships is not fully implemented`,
        );
      } catch (error) {
        console.warn(
          `Failed to resolve tenant through ${check.field} for ${model}:`,
          error,
        );
      }
    }
  }

  return { tenantId: undefined, userId: entityResult?.userId };
}

/**
 * Extends Prisma Client with extension for automatic webhook triggering
 * This extension automatically triggers webhooks for specific models and actions
 *
 * @param options Configuration options for webhook triggering
 * @returns The Prisma Client extension
 */
export function extendWebhookTriggerMiddleware(
  options: WebhookTriggerOptions = {},
) {
  const {
    requireContext = true,
    defaultTenantId,
    skipOnMissingContext = false,
    allowSystemTriggers = true,
  } = options;

  return Prisma.defineExtension({
    name: "webhookTrigger",
    query: {
      $allModels: {
        async $allOperations({ model, operation, args, query }) {
          // For delete and update operations, capture entity data before operation
          let preOperationEntity: any = null;
          if (
            (operation === "delete" || operation === "update") &&
            Object.keys(WEBHOOK_EVENT_MAP).includes(model) &&
            args.where?.id
          ) {
            try {
              // Fetch the entity before operation to capture current state
              const entityQuery = (query as any).findUnique({
                where: { id: args.where.id },
                select: {
                  id: true,
                  tenantId: true,
                  userId: true,
                  createdById: true,
                  assigneeId: true,
                  dealOwner: true, // For Opportunity model
                  // Add fields that might trigger additional events
                  ...(model === "SupportTicket" && {
                    status: true,
                    priority: true,
                    assigneeId: true,
                    ticketNumber: true,
                    subject: true,
                    description: true,
                    type: true,
                    tags: true,
                    channel: true,
                    contactId: true,
                    deleted: true,
                  }),
                  ...(model === "Lead" && {
                    firstName: true,
                    lastName: true,
                    email: true,
                    phone: true,
                    company: true,
                    status: true,
                    source: true,
                    assigneeId: true,
                    deleted: true,
                  }),
                  ...(model === "Opportunity" && {
                    dealName: true,
                    amount: true,
                    stage: true,
                    probability: true,
                    closeDate: true,
                    accountId: true,
                    contactId: true,
                    dealOwner: true,
                    deleted: true,
                  }),
                  ...(model === "Task" && {
                    title: true,
                    description: true,
                    status: true,
                    priority: true,
                    dueDate: true,
                    assigneeId: true,
                    userId: true,
                    deleted: true,
                  }),
                  ...(model === "Contact" && {
                    firstName: true,
                    lastName: true,
                    email: true,
                    phoneNumber: true,
                    accountId: true,
                    deleted: true,
                  }),
                  ...(model === "BusinessAccount" && {
                    name: true,
                    website: true,
                    industry: true,
                    size: true,
                    deleted: true,
                  }),
                  ...(model === "Campaign" && {
                    name: true,
                    type: true,
                    description: true,
                    status: true,
                    startDate: true,
                    endDate: true,
                    budget: true,
                    targetAudience: true,
                    deleted: true,
                  }),
                  ...(model === "Note" && {
                    title: true,
                    content: true,
                    reference_modal: true,
                    reference_modalId: true,
                    authorId: true,
                    deleted: true,
                  }),
                  ...(model === "TicketAttachment" && {
                    fileName: true,
                    originalName: true,
                    fileSize: true,
                    mimeType: true,
                    fileUrl: true,
                    isPublic: true,
                    ticketId: true,
                    uploadedById: true,
                  }),
                  // Add other model-specific fields as needed
                },
              });
              preOperationEntity = await entityQuery;
            } catch (error) {
              console.warn(
                `Failed to fetch entity before ${operation} for ${model}:`,
                error,
              );
            }
          }

          // Execute the query
          const result = await query(args);
          // Only proceed with webhook triggering for supported models and operations
          const supportedModels = Object.keys(WEBHOOK_EVENT_MAP);

          console.log(
            "Webhook trigger middleware called for",
            model,
            operation,
          );

          if (
            supportedModels.includes(model) &&
            ["create", "update", "delete"].includes(operation)
          ) {
            try {
              // Extract tenant ID and user ID from query result or pre-operation entity
              const entityForExtraction =
                operation === "delete" ? preOperationEntity : result;
              const { tenantId: resultTenantId, userId: resultUserId } =
                await extractTenantAndUserFromResult(
                  model,
                  entityForExtraction,
                  operation,
                  args,
                  query, // Pass the prisma instance for potential lookups
                );

              // Get context (userId, tenantId) from the async local storage
              const context = getContext();

              let tenantId: string | undefined;
              let userId: string | undefined;

              // Prioritize tenant ID from result, then context, then defaults
              if (resultTenantId) {
                tenantId = resultTenantId;
              } else if (context?.tenantId) {
                tenantId = context.tenantId;
              } else if (!requireContext) {
                tenantId = defaultTenantId;
              }

              // Prioritize user ID from result, then context
              if (resultUserId) {
                userId = resultUserId;
              } else if (context?.userId) {
                userId = context.userId;
              }

              console.log("Extracted tenantId and userId:", tenantId, userId);

              if (requireContext) {
                // Require context or result to have tenant ID
                if (!tenantId) {
                  console.warn(
                    `Webhook triggering skipped: Missing tenant ID for ${model} ${operation}`,
                  );
                  return result;
                }
              } else {
                // Use tenant ID from result, context, or defaults
                if (!tenantId) {
                  if (skipOnMissingContext && !defaultTenantId) {
                    console.warn(
                      `Webhook triggering skipped: No tenant ID available for ${model} ${operation}`,
                    );
                    return result;
                  }

                  if (!defaultTenantId && !allowSystemTriggers) {
                    console.warn(
                      `Webhook triggering skipped: No tenant ID and system triggers not allowed for ${model} ${operation}`,
                    );
                    return result;
                  }
                }
              }

              // Get the event type for this model and operation
              const eventMap =
                WEBHOOK_EVENT_MAP[model as keyof typeof WEBHOOK_EVENT_MAP];
              if (!eventMap) {
                return result;
              }

              let eventType = eventMap[operation as keyof typeof eventMap];
              if (!eventType) {
                return result;
              }

              // Special handling for Note model - check if it's related to a ticket
              if (model === "Note" && operation === "create") {
                const noteResult = result as any;
                if (noteResult?.reference_modal === "SupportTicket") {
                  eventType = "ticket.note_added";
                }
              }

              // Special handling for TicketAttachment model
              if (model === "TicketAttachment" && operation === "create") {
                eventType = "ticket.attachment_added";
              }

              // Extract webhook data
              let webhookData;

              if (operation === "delete") {
                // For delete operations, use pre-operation entity data if available
                if (preOperationEntity) {
                  webhookData = {
                    ...extractWebhookData(model, preOperationEntity),
                    deletedAt: new Date().toISOString(),
                  };
                } else {
                  // Fallback to basic delete data
                  webhookData = {
                    id: args.where?.id,
                    deletedAt: new Date().toISOString(),
                  };
                }
              } else {
                webhookData = extractWebhookData(model, result);
              }

              // Detect changes for update operations
              let changes: Record<string, { from: any; to: any }> | undefined;
              if (operation === "update") {
                changes = detectChanges(args, preOperationEntity);
              }

              // Add user context if available
              const payload = {
                [model.toLowerCase()]: webhookData,
                ...(userId && {
                  updatedBy: {
                    id: userId,
                  },
                }),
              };
              console.log("Webhook payload:", payload, changes);

              // Trigger webhooks asynchronously
              if (tenantId) {
                // Don't await to avoid blocking the original operation
                setImmediate(async () => {
                  // Trigger main webhook event
                  await triggerWebhooks(tenantId, eventType, payload, changes);

                  // Trigger additional events for update operations
                  if (operation === "update" && changes) {
                    await triggerAdditionalEvents(
                      model,
                      changes,
                      tenantId,
                      webhookData,
                    );
                  }
                });
              }
            } catch (error) {
              // Log the error but don't block the original operation
              console.error(
                `Error triggering webhooks for ${model} ${operation}:`,
                error,
              );
            }
          }

          return result;
        },
      },
    },
  });
}

/**
 * Creates webhook triggers without requiring context (context-free)
 * Useful for API routes, background jobs, system operations, etc.
 */
export function extendWebhookTriggerContextFree(options?: {
  defaultTenantId?: string;
  allowSystemTriggers?: boolean;
}) {
  return extendWebhookTriggerMiddleware({
    requireContext: false,
    skipOnMissingContext: false,
    ...options,
  });
}

/**
 * Creates webhook triggers for system operations only (no user context)
 * Useful for background processes, automated tasks, etc.
 */
export function extendSystemWebhookTrigger(defaultTenantId?: string) {
  return extendWebhookTriggerMiddleware({
    requireContext: false,
    defaultTenantId,
    skipOnMissingContext: false,
    allowSystemTriggers: true,
  });
}
