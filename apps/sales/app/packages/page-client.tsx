"use client";

import type { Package } from "@/types/package";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDelete } from "@flinkk/hooks";
import { getPackagesTableColumns } from "./columns";
import { useRouter } from "next/navigation";

import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog,
    ),
  {
    ssr: false,
  },
);

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface PackagesTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

export function PackagesTable({
  data,
  pageCount,
  permissions,
}: PackagesTableProps) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Package> | null>(null);

  const { deleteRecord } = useDelete({
    model: "package",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setRowAction(null);
      router.refresh();
    },
  });

  const handleDeletePackage = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      await deleteRecord(rowAction.row.id);
    }
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    } else if (rowAction?.type === "view" && rowAction.row) {
      // Navigate to the view page for the package with proper title
      const packageName = rowAction.row.name || "Unnamed Package";
      router.push(
        `/packages/${rowAction.row.id}/view?title=${encodeURIComponent(packageName)}`,
      );
      setRowAction(null);
    } else if (rowAction?.type === "edit" && rowAction.row) {
      // Navigate to the edit page for the package with proper title
      const packageName = rowAction.row.name || "Unnamed Package";
      router.push(
        `/packages/${rowAction.row.id}/edit?title=${encodeURIComponent(`Edit - ${packageName}`)}`,
      );
      setRowAction(null);
    }
  }, [rowAction, router]);

  const columns = React.useMemo(
    () =>
      getPackagesTableColumns({
        setRowAction,
      }),
    [setRowAction],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Use package name for the tab title
          const packageName = row.name || "Unnamed Package";

          // Create URL with title parameter
          const url = `/packages/${row.id}/view?title=${encodeURIComponent(packageName)}`;
          router.push(url);
        }}
        doctype="package"
      >
        <DataTableToolbar
          table={table}
          buttonText={permissions.canCreate ? "New Package" : undefined}
          href={permissions.canCreate ? "/packages/new" : undefined}
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeletePackage}
        />
      )}
    </>
  );
}
