"use client";

import type { Opportunity } from "@/types/opportunity";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDelete } from "@flinkk/hooks";
import { getOpportunitiesTableColumns } from "./columns";
import { useRouter } from "next/navigation";

import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog,
    ),
  {
    ssr: false,
  },
);

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface OpportunitiesTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
  hasInventoryConfigured: boolean;
}

export function OpportunitiesTable({
  data: initialData,
  pageCount,
  permissions,
  hasInventoryConfigured,
}: OpportunitiesTableProps) {
  const router = useRouter();
  const [data, setData] = React.useState(initialData);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Opportunity> | null>(null);

  // Update local data when initial data changes (e.g., after refresh)
  React.useEffect(() => {
    setData(initialData);
  }, [initialData]);

  const { deleteRecord, isLoading: isDeleting } = useDelete({
    model: "opportunity",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setRowAction(null);
      // Refresh the page to get updated data from server
      router.refresh();
    },
  });

  const handleDeleteOpportunity = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      const opportunityToDelete = rowAction.row;

      // Optimistically remove the item from the UI
      setData(
        (prevData: Opportunity[] | undefined) =>
          prevData?.filter(
            (item: Opportunity) => item.id !== opportunityToDelete.id,
          ) || [],
      );

      try {
        await deleteRecord(opportunityToDelete.id);
        // The onSuccess callback will handle router.refresh()
      } catch (error) {
        // If deletion fails, restore the item to the UI
        setData((prevData: Opportunity[] | undefined) =>
          prevData ? [...prevData, opportunityToDelete] : [opportunityToDelete],
        );
        // The error toast is handled by the useDelete hook
      }
    }
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    }
  }, [rowAction]);

  const columns = React.useMemo(
    () =>
      getOpportunitiesTableColumns({
        setRowAction,
        permissions,
      }),
    [setRowAction, permissions],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Get opportunity name for the tab title
          const opportunityName =
            row.dealName || row.name || "Unnamed Opportunity";

          // Create URL with title parameter
          const url = `/opportunities/${row.id}/view?title=${encodeURIComponent(opportunityName)}`;
          router.push(url);
        }}
        doctype="opportunity"
      >
        <DataTableToolbar
          table={table}
          buttonText={
            permissions.canCreate && !hasInventoryConfigured
              ? "New Opportunity"
              : undefined
          }
          href={
            permissions.canCreate && !hasInventoryConfigured
              ? "/opportunities/new"
              : undefined
          }
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeleteOpportunity}
        />
      )}
    </>
  );
}
