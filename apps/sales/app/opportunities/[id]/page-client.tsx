"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { useFormCancellation } from "@flinkk/hooks/form";
import useFormPersist from "react-hook-form-persist";
import { Button } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { Loader2Icon } from "lucide-react";
import SectionLoading from "./sections/loading";
import { useRouter } from "next/navigation";

// Import our dynamic form elements
import { DynamicSelectFieldFormElement } from "@flinkk/dynamic-form/form-elements";

import { CustomFieldType } from "@/features/crm/components/custom-fields-form";

/* Dynamic sections */
const BasicInformation = dynamic(
  () => import("./sections/01-basic-information"),
  { loading: () => <SectionLoading /> }
);
const SalesPipeline = dynamic(() => import("./sections/02-sales-pipeline"), {
  loading: () => <SectionLoading />,
});
const AdditionalInformation = dynamic(
  () => import("./sections/03-additional-information"),
  { loading: () => <SectionLoading /> }
);
const CustomFieldsSection = dynamic(
  () => import("./sections/04-custom-fields"),
  { loading: () => <SectionLoading /> }
);

import { FieldPermissionsData } from "@flinkk/shared-auth/utils/field-permissions-utils";
import { useCurrencyFormatterV2 } from "@flinkk/shared-hooks/use-currency-formatter-v2";

// Related Entity Select Component
interface RelatedEntitySelectProps {
  control: any;
  name: string;
  relatedToTypeName: string;
  label: string;
  required?: boolean;
  placeholder?: string;
}

const RelatedEntitySelect: React.FC<RelatedEntitySelectProps> = ({
  control,
  name,
  relatedToTypeName,
  label,
  required = false,
  placeholder = "Select entity",
}) => {
  // Watch the relatedToType field to determine which dynamic key to use
  const relatedToType = useWatch({
    control,
    name: relatedToTypeName,
  });

  // Map relatedToType to dynamic keys
  const getDynamicKey = (type: string): string => {
    switch (type) {
      case "LEAD":
        return "leads";
      case "CONTACT":
        return "contacts";
      case "ACCOUNT":
        return "accounts";
      case "OPPORTUNITY":
        return "opportunities";
      case "TASK":
        return "tasks";
      default:
        return "contacts"; // fallback
    }
  };

  // Don't render the dynamic select if no relatedToType is selected
  if (!relatedToType) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium text-muted-foreground">
          {label}
          {required && <span className="text-destructive">*</span>}
        </label>
        <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground">
          {placeholder}
        </div>
        <p className="text-xs text-muted-foreground">
          Please select a "Related To Type" first
        </p>
      </div>
    );
  }

  const dynamicKey = getDynamicKey(relatedToType);

  return (
    <DynamicSelectFieldFormElement
      control={control}
      name={name}
      label={label}
      required={required}
      placeholder={placeholder}
      dynamicKey={dynamicKey}
      allowNone={false}
    />
  );
};

// Validation function

// Zod schema for opportunity form validation
const opportunityFormSchema = z.object({
  dealName: z
    .string()
    .min(1, "Opportunity name is required")
    .max(100, "Opportunity name must be less than 100 characters"),
  description: z
    .string()
    .min(1, "Description is required")
    .max(1000, "Description must be less than 1000 characters"),
  relatedToType: z.enum(["LEAD", "OPPORTUNITY", "CONTACT", "ACCOUNT", "TASK"]),
  relatedToId: z.string().min(1, "Related entity is required"),
  dealOwner: z.string().min(1, "Opportunity owner is required"),
  stage: z.string().min(1, "Stage is required"),
  status: z.string().min(1, "Status is required"),
  value: z
    .number()
    .min(0, "Value must be a positive number")
    .max(*********, "Value is too large"),
  currency: z.enum(["USD", "CHF", "EUR", "GBP"]),
  expectedCloseDate: z.string().min(1, "Expected close date is required"),
  actualCloseDate: z.string().optional(),
  source: z.string().optional(),
  campaignId: z.string().optional(),
  tags: z.string().optional(),
  convertedFromLeadId: z.string().optional(),
  lastContactedAt: z.string().optional(),
  stageEnteredAt: z.string().optional(),
  probability: z.number().min(0).max(100).optional(),
  bookingId: z.string().optional(),
  fulfillmentId: z.string().optional(),
  notes: z.string().optional(),
});

type OpportunityFormValues = z.infer<typeof opportunityFormSchema>;

interface NewOpportunityFormProps {
  id?: string;
  initialData?: any;
  opportunityStageOptions?: any[];
  currencyOptions?: any[];
  relatedToTypeOptions?: any[];
  fieldPermissionsData: FieldPermissionsData;
  customFields?: CustomField[];
}

// Removed User and Campaign interfaces since we're using DynamicSelectFieldFormElement

interface CustomField {
  id: string;
  name: string;
  label: string;
  description?: string | null;
  type: string;
  isRequired: boolean;
  defaultValue?: string | null;
  placeholder?: string | null;
  helpText?: string | null;
  options?: any;
  value?: string | null;
}

export function NewOpportunityForm({
  id,
  initialData,
  opportunityStageOptions = [],
  currencyOptions = [],
  relatedToTypeOptions = [],
  customFields = [],
}: NewOpportunityFormProps) {
  console.log("Client component - relatedToTypeOptions:", relatedToTypeOptions);
  console.log("Client component - currencyOptions:", currencyOptions);
  const router = useRouter();
  const isEditMode = id !== "new" && initialData;

  // Dynamic currency formatter
  const { currencyConfig, isLoading: currencyLoading } =
    useCurrencyFormatterV2();

  // Storage key for form persistence
  const storageKey = `opportunity-form-${id}`;
  const shouldPersist = id === "new";

  // Flag to prevent saving after successful submission
  const [isSubmissionSuccessful, setIsSubmissionSuccessful] = useState(false);

  // Use the new useSaveFormData hook with built-in toast notifications
  const { save: saveOpportunity, isLoading: isSaving } = useSaveFormData({
    model: "opportunity",
    refreshAfterCreate: false,
    clearFormPersistence: shouldPersist
      ? () => {
          if (typeof window !== "undefined") {
            setIsSubmissionSuccessful(true);
            sessionStorage.removeItem(storageKey);
            sessionStorage.removeItem("opportunity-form-new");
          }
        }
      : undefined,
    goBack: true,
  });

  // Initialize React Hook Form with Zod validation
  const form = useForm<OpportunityFormValues>({
    resolver: zodResolver(opportunityFormSchema),
    defaultValues: {
      dealName: initialData?.dealName || initialData?.name || "",
      description: initialData?.description || initialData?.notes || "",
      relatedToType: initialData?.relatedToType || "",
      relatedToId: initialData?.relatedToId || "",
      dealOwner: initialData?.dealOwner || initialData?.userId || "",
      stage: initialData?.stage || "",
      status: initialData?.status || "OPEN",
      value: initialData?.value || 0,
      currency: currencyConfig?.currencyCode || initialData?.currency || "USD",
      expectedCloseDate: initialData?.expectedCloseDate
        ? new Date(initialData.expectedCloseDate).toISOString().split("T")[0]
        : "",
      actualCloseDate: initialData?.actualCloseDate
        ? new Date(initialData.actualCloseDate).toISOString().split("T")[0]
        : "",
      source: initialData?.source || "",
      campaignId: initialData?.campaignId || "",
      tags: initialData?.tags || "",
      convertedFromLeadId: initialData?.convertedFromLeadId || "",
      lastContactedAt: initialData?.lastContactedAt || "",
      stageEnteredAt: initialData?.stageEnteredAt || "",
      probability: initialData?.probability || 0,
      bookingId: initialData?.bookingId || "",
      fulfillmentId: initialData?.fulfillmentId || "",
      notes: initialData?.notes || "",
    },
  });

  // Set up form persistence for new opportunities
  useFormPersist(storageKey, {
    watch: form.watch,
    setValue: form.setValue,
    storage:
      shouldPersist && typeof window !== "undefined" && !isSubmissionSuccessful
        ? window.sessionStorage
        : undefined,
  });

  // Handle form cancellation with navigation cleanup
  const { handleCancel } = useFormCancellation({
    recordId: id,
    redirectPath: "/opportunities",
    clearFormPersistence: shouldPersist
      ? () => {
          if (typeof window !== "undefined") {
            sessionStorage.removeItem(storageKey);
          }
        }
      : undefined,
  });

  const [customFieldValues, setCustomFieldValues] = useState<
    Record<string, any>
  >({});

  // Initialize custom field values from initial data
  useEffect(() => {
    if (initialData?.customFields) {
      const initialCustomFieldValues: Record<string, any> = {};
      initialData.customFields.forEach((field: any) => {
        if (field.value !== null) {
          initialCustomFieldValues[field.id] = field.value;
        }
      });
      setCustomFieldValues(initialCustomFieldValues);
    }
  }, [initialData]);

  // Handle custom field changes
  const handleCustomFieldChange = (fieldId: string, value: string | null) => {
    setCustomFieldValues((prev) => ({
      ...prev,
      [fieldId]: value,
    }));
  };

  // Prepare custom fields with values for the form
  const customFieldsWithValues = customFields.map((field) => ({
    ...field,
    value: customFieldValues[field.id] || field.defaultValue || null,
    isEditableByDefault: true,
    type:
      CustomFieldType[field.type as keyof typeof CustomFieldType] ||
      CustomFieldType.TEXT,
  }));

  // Handle form submission using the new hook
  const onSubmit = async (data: OpportunityFormValues) => {
    // Debug: Log the form data to see what's being submitted
    console.log("Form submission data:", data);
    console.log("relatedToId value:", data.relatedToId);
    console.log("relatedToType value:", data.relatedToType);

    // Prepare the data for submission
    const submissionData = {
      ...data,
      customFields: Object.entries(customFieldValues).map(([id, value]) => ({
        id,
        value,
      })),
    };

    try {
      // Use the new save function from the hook
      await saveOpportunity(id, submissionData);

      // Additional cleanup after successful submission
      if (shouldPersist && typeof window !== "undefined") {
        setIsSubmissionSuccessful(true);
        sessionStorage.removeItem(storageKey);
        sessionStorage.removeItem("opportunity-form-new");
      }
    } catch (error) {
      // Don't clear storage if submission failed
      throw error;
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Sections (dynamically loaded) */}
        <BasicInformation
          control={form.control}
          relatedToTypeOptions={relatedToTypeOptions as any}
          currencyOptions={currencyOptions as any}
          RelatedEntitySelect={RelatedEntitySelect}
        />

        <SalesPipeline control={form.control} />

        <AdditionalInformation control={form.control} />

        {/* Custom Fields Section (dynamically loaded) */}
        <CustomFieldsSection
          customFieldsWithValues={customFieldsWithValues as any}
          onChange={handleCustomFieldChange}
        />

        {/* Submit Button */}
        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
            {isEditMode ? "Update Opportunity" : "Create Opportunity"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
